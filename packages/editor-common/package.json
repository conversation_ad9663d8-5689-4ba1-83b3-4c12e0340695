{"name": "@repo/editor-common", "version": "1.0.0", "private": true, "exports": {".": {"types": "./dist/types/index.d.ts", "import": "./dist/esm/index.js", "require": "./dist/cjs/index.js"}, "./*": {"types": "./dist/types/*.d.ts", "import": "./dist/esm/*.js", "require": "./dist/cjs/*.js"}}, "files": ["./dist/**"], "publishConfig": {"access": "public"}, "scripts": {"build": "npm run build:clean && npm run build:esm && npm run build:cjs && npm run build:types && echo '✅ @repo/common: Build completed successfully!'", "build:clean": "rm -rf dist", "build:esm": "tsc -p tsconfig.esm.json && echo '{\"type\":\"module\"}' > dist/esm/package.json", "build:cjs": "tsc -p tsconfig.cjs.json && echo '{\"type\":\"commonjs\"}' > dist/cjs/package.json", "build:types": "tsc -p tsconfig.types.json", "tsc": "tsc", "tsc:clean": "tsc --build --clean", "tsc:check": "tsc --noEmit", "typecheck": "tsc --noEmit", "lint": "biome check . --diagnostic-level=error", "lint:fix": "biome check . --write --diagnostic-level=error", "format": "biome format . --write", "format:check": "biome format .", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "ci": "pnpm run lint:fix && pnpm run tsc:check && pnpm run test"}, "dependencies": {"markdown-it": "catalog:", "markdown-it-mark": "catalog:", "markdown-it-underline": "catalog:", "markdown-it-task-lists": "catalog:", "@tiptap/core": "catalog:", "highlight.js": "catalog:", "@tiptap/react": "catalog:", "@tiptap/pm": "catalog:", "prosemirror-markdown": "catalog:", "jsdom": "catalog:", "yjs": "catalog:", "y-prosemirror": "catalog:", "@tiptap/extension-highlight": "catalog:", "@tiptap/extension-mathematics": "catalog:", "@tiptap/extension-blockquote": "catalog:", "@tiptap/extension-bold": "catalog:", "@tiptap/extension-bullet-list": "catalog:", "@tiptap/extension-code": "catalog:", "@tiptap/extension-code-block": "catalog:", "@tiptap/extension-document": "catalog:", "@tiptap/extension-hard-break": "catalog:", "@tiptap/extension-horizontal-rule": "catalog:", "@tiptap/extension-image": "catalog:", "@tiptap/extension-italic": "catalog:", "@tiptap/extension-link": "catalog:", "@tiptap/extension-list-item": "catalog:", "@tiptap/extension-ordered-list": "catalog:", "@tiptap/extension-paragraph": "catalog:", "@tiptap/extension-placeholder": "catalog:", "@tiptap/extension-strike": "catalog:", "@tiptap/extension-table": "catalog:", "@tiptap/extension-table-cell": "catalog:", "@tiptap/extension-table-header": "catalog:", "@tiptap/extension-table-row": "catalog:", "@tiptap/extension-task-item": "catalog:", "@tiptap/extension-task-list": "catalog:", "@tiptap/extension-text": "catalog:", "@tiptap/extension-underline": "catalog:"}, "devDependencies": {"@jest/types": "^29.6.3", "@repo/jest-config": "workspace:*", "@types/jest": "^29.5.14", "@types/node": "catalog:", "jest": "^29.7.0", "ts-jest": "^29.2.5", "@types/jsdom": "^21.1.7", "@types/markdown-it": "^14.1.2", "typescript": "catalog:"}}