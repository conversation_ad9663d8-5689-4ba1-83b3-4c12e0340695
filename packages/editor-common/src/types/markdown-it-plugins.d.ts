/**
 * Type declarations for markdown-it plugins
 * This file provides type definitions for markdown-it plugin imports
 */

declare module 'markdown-it-mark' {
  import type { PluginSimple } from 'markdown-it';
  const markdownitMark: PluginSimple;
  export default markdownitMark;
}

declare module 'markdown-it-task-lists' {
  import type { PluginSimple } from 'markdown-it';
  const taskListPlugin: PluginSimple;
  export default taskListPlugin;
}

declare module 'markdown-it-underline' {
  import type { PluginSimple } from 'markdown-it';
  const underline: PluginSimple;
  export default underline;
}
