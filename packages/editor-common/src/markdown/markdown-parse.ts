import markdownit from 'markdown-it';
import markdownitMark from 'markdown-it-mark';
import taskListPlugin from 'markdown-it-task-lists';
import markdownitUnderline from 'markdown-it-underline';
import { DOMNormalizer } from './dom-normalizer';
import { htmlParser } from './html-parser';
import { RendererManager } from './renderer-manager';
import { BaseRenderer } from './renderers';

export class MarkdownParse {
  private mdIt: markdownit;
  private rendererManager: RendererManager;

  constructor() {
    this.rendererManager = new RendererManager();
    this.mdIt = this.initMarkdownIt();
  }

  private initMarkdownIt(): markdownit {
    const mdIt = markdownit({
      html: true,
      breaks: true,
      linkify: true,
    });

    // Add plugins
    this.setupPlugins(mdIt);

    // Configure settings
    mdIt.set({ langPrefix: '' });

    // Setup custom renderer rules via manager
    this.rendererManager.setupAllRules(mdIt);

    return mdIt;
  }

  private setupPlugins(mdIt: markdownit): void {
    mdIt.use(markdownitMark);
    mdIt.use(taskListPlugin);
    mdIt.use(markdownitUnderline);
  }

  parse(content: string): string {
    // Convert literal \n to actual newlines for markdown-it breaks
    const processedContent = content.replace(/\\n/g, '\n');

    // Render markdown to HTML
    const renderHTML = this.mdIt.render(processedContent);

    // Parse HTML and normalize DOM
    const bodyElement = htmlParser.parseHTML(renderHTML);
    const normalizedElement = DOMNormalizer.normalize(bodyElement);

    return normalizedElement.innerHTML;
  }

  // Allow external registration of custom renderers
  registerRenderer(renderer: BaseRenderer): void {
    this.rendererManager.registerRenderer(renderer);
    renderer.setupRules(this.mdIt);
  }
}

export const markdownParse = new MarkdownParse();
