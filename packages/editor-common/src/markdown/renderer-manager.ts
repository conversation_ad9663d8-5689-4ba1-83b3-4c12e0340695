import type markdownit from 'markdown-it';
import {
  <PERSON><PERSON><PERSON><PERSON>,
  Code<PERSON>lock<PERSON><PERSON>er,
  EmptyParagraphRenderer,
  ImageRenderer,
  LineBreakRenderer,
  LinkRenderer,
  TextRenderer,
} from './renderers';

export class RendererManager {
  private renderers: BaseRenderer[] = [];

  constructor() {
    // Register all default renderers
    this.registerRenderer(new EmptyParagraphRenderer());
    this.registerRenderer(new CodeBlockRenderer());
    this.registerRenderer(new ImageRenderer());
    this.registerRenderer(new LinkRenderer());
    this.registerRenderer(new TextRenderer());
    this.registerRenderer(new LineBreakRenderer());
  }

  registerRenderer(renderer: BaseRenderer): void {
    this.renderers.push(renderer);
  }

  setupAllRules(md: markdownit): void {
    // Apply render token wrapper globally
    const originalRenderToken = md.renderer.renderToken.bind(md.renderer);
    md.renderer.renderToken = this.withoutNewLine(originalRenderToken);

    // Setup rules for each renderer
    this.renderers.forEach((renderer) => {
      renderer.setupRules(md);
    });
  }

  private withoutNewLine(renderer: any) {
    return (...args: any[]) => {
      const rendered = renderer(...args);
      if (rendered === '\n') {
        return rendered;
      }
      if (rendered[rendered.length - 1] === '\n') {
        return rendered.slice(0, -1);
      }
      return rendered;
    };
  }
}
