import type markdownit from 'markdown-it';
import { TokenRenderer } from './base';

export class TextRenderer extends TokenRenderer {
  getName(): string {
    return 'text';
  }

  setupRules(md: markdownit): void {
    const defaultTextRenderer =
      md.renderer.rules.text ||
      ((tokens, idx, options, _env, self) => self.renderToken(tokens, idx, options));

    md.renderer.rules.text = (tokens, idx, options, _env, self) => {
      const token = tokens[idx];
      if (token?.hidden) {
        return '';
      }
      return defaultTextRenderer(tokens, idx, options, _env, self);
    };
  }
}
