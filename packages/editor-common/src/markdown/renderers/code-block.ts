import type markdownit from 'markdown-it';
import { Token<PERSON>enderer } from './base';

export class CodeBlockRenderer extends TokenRenderer {
  getName(): string {
    return 'fence';
  }

  setupRules(md: markdownit): void {
    md.renderer.rules.fence = this.renderFence.bind(this);
    md.renderer.rules.code_block = this.withoutNewLine(md.renderer.rules.code_block);
  }

  private renderFence(tokens: any[], idx: number): string {
    const token = tokens[idx];
    if (!token) return '';

    const info = token.info ? token.info.trim() : '';
    const content = token.content || '';

    // Check for special code block types
    if (info === 'mermaid') {
      return this.renderMermaid(content);
    }

    // Handle regular code blocks
    return this.renderRegularCode(info, content);
  }

  private renderMermaid(content: string): string {
    return `<div data-type="mermaid" data-mermaid-container=""><pre data-mermaid-content=""><code>${content}</code></pre></div>`;
  }

  private renderRegularCode(info: string, content: string): string {
    const language = info.split(/\s+/)[0];
    if (language) {
      return `<pre class="${language}"><code class="${language}">${content}</code></pre>`;
    }
    return `<pre><code>${content}</code></pre>`;
  }
}
