import type markdownit from 'markdown-it';
import { generateImageUniqueId, ImageStatus } from '../../const/image';
import type { ImageAttrs } from '../../extensions/image';
import { BaseRenderer } from './base';

export class ImageRenderer extends BaseRenderer {
  getName(): string {
    return 'image';
  }

  setupRules(md: markdownit): void {
    md.renderer.rules.image = this.renderImage.bind(this);
  }

  private renderImage(tokens: any[], idx: number): string {
    const token = tokens[idx];
    if (!token) return '';

    try {
      const alt = token.content || token.attrs?.find(([key]: [string]) => key === 'alt')?.[1] || '';
      const src = token.attrs?.find(([key]: [string]) => key === 'src')?.[1] || '';

      // Delegate to specific render methods based on file type
      if (this.isSvg(src)) {
        return this.renderSvg(src, alt);
      }

      if (this.isAudio(src)) {
        return this.renderAudio(src, alt);
      }

      return this.renderRegularImage(src, alt);
    } catch (error) {
      console.error('Error parsing image:', error);
      return token.markup || '';
    }
  }

  private isSvg(src: string): boolean {
    return src.toLowerCase().endsWith('.svg');
  }

  private isAudio(src: string): boolean {
    return src.toLowerCase().endsWith('.mp3');
  }

  private renderSvg(src: string, alt: string): string {
    return `<div data-type="SvgEditor" data-src="${src}" data-alt="${alt}"></div>`;
  }

  private renderAudio(src: string, alt: string): string {
    return `<div data-type="Audio" data-src="${src}" data-alt="${alt}"></div>`;
  }

  private renderRegularImage(src: string, alt: string): string {
    let attrs: ImageAttrs = {};
    try {
      attrs = JSON.parse(alt);
    } catch {
      attrs = { alt, status: ImageStatus.LOADING };
    }

    const htmlAttrs = {
      src,
      id: attrs.id || generateImageUniqueId(),
      status: attrs.status,
      width: attrs.width,
      height: attrs.height,
      alt: attrs.alt || alt,
      text: attrs.text || '',
    };

    const attrString = Object.entries(htmlAttrs)
      .map(([key, value]) => `${key}="${value}"`)
      .join(' ');

    return `<img ${attrString}>`;
  }
}
