import type markdownit from 'markdown-it';

// Base renderer class that all specific renderers extend
export abstract class BaseRenderer {
  abstract getName(): string;
  abstract setupRules(md: markdownit): void;
}

// Base class for token renderers
export abstract class TokenRenderer extends BaseRenderer {
  protected withoutNewLine(renderer: any) {
    return (...args: any[]) => {
      const rendered = renderer(...args);
      if (rendered === '\n') {
        return rendered;
      }
      if (rendered[rendered.length - 1] === '\n') {
        return rendered.slice(0, -1);
      }
      return rendered;
    };
  }
}
