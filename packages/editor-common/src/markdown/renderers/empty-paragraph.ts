import type markdownit from 'markdown-it';
import { EMPTY_PARAGRAPH_NODE_MARKDOWN_VAL } from '../const';
import { BaseRenderer } from './base';

export class EmptyParagraphRenderer extends BaseRenderer {
  getName(): string {
    return 'empty_paragraph';
  }

  setupRules(md: markdownit): void {
    // Add block rule for detecting empty paragraphs
    md.block.ruler.before('paragraph', this.getName(), (state, start, _end, silent) => {
      const pos = (state.bMarks?.[start] || 0) + (state.tShift?.[start] || 0);
      const max = state.eMarks?.[start] || 0;

      const lineContent = state.src.slice(pos, max).trim();
      if (lineContent !== EMPTY_PARAGRAPH_NODE_MARKDOWN_VAL) return false;

      if (silent) return true;

      const token = state.push(this.getName(), 'p', 0);
      token.content = '';
      token.map = [start, start + 1];

      state.line = start + 1;
      return true;
    });

    // Add render rule
    md.renderer.rules[this.getName()] = () => '<p></p>';
  }
}
