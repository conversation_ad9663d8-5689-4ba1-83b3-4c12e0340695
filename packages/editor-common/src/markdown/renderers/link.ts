import type markdownit from 'markdown-it';
import { TokenRenderer } from './base';

export class LinkRenderer extends TokenRenderer {
  getName(): string {
    return 'link';
  }

  setupRules(md: markdownit): void {
    const defaultLinkRenderer =
      md.renderer.rules.link_open ||
      ((tokens, idx, options, _env, self) => self.renderToken(tokens, idx, options));

    const defaultLinkCloseRenderer =
      md.renderer.rules.link_close ||
      ((tokens, idx, options, _env, self) => self.renderToken(tokens, idx, options));

    md.renderer.rules.link_open = (tokens, idx, options, _env, self) => {
      return defaultLinkRenderer(tokens, idx, options, _env, self);
    };

    md.renderer.rules.link_close = (tokens, idx, options, _env, self) => {
      const token = tokens[idx];
      if (token?.hidden) {
        return '';
      }
      return defaultLinkCloseRenderer(tokens, idx, options, _env, self);
    };
  }
}
