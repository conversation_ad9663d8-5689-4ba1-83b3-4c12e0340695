let parseHTMLImpl: ((content: string) => HTMLElement) | null = null;

// Initialize parser based on environment at module load time
if (typeof window !== 'undefined') {
  // Browser environment
  import('@tiptap/core').then(({ elementFromString }) => {
    parseHTMLImpl = (content: string) => elementFromString(content);
  });
} else {
  // Node environment
  import('jsdom').then(({ JSDOM }) => {
    parseHTMLImpl = (content: string) => {
      const dom = new JSDOM(`
        <!doctype html>
        <html lang="en">
        <head></head>
        <body>${content}</body>
        </html>
      `);
      return dom.window.document.body;
    };
  });
}

export class HTMLParserFactory {
  private static instance: HTMLParserFactory;

  private constructor() {}

  static getInstance(): HTMLParserFactory {
    if (!HTMLParserFactory.instance) {
      HTMLParserFactory.instance = new HTMLParserFactory();
    }
    return HTMLParserFactory.instance;
  }

  parseHTML(content: string): HTMLElement {
    if (!parseHTMLImpl) {
      throw new Error(
        'HTML parser not yet initialized. This usually happens when trying to use the parser immediately after import.',
      );
    }
    return parseHTMLImpl(content);
  }
}

// Export singleton instance
export const htmlParser = HTMLParserFactory.getInstance();
