import { EditorSchema } from '../extensions';
import { extractElement } from './util';

const NODE_TEXT_NODE = 3;

export class DOMNormalizer {
  private static removeWhitespaces(node: HTMLElement): HTMLElement {
    const children = node.childNodes;

    for (let i = children.length - 1; i >= 0; i -= 1) {
      const child = children[i];
      if (!child) {
        continue;
      }

      if (
        child.nodeType === NODE_TEXT_NODE &&
        child.nodeValue &&
        /^(\n\s\s|\n)$/.test(child.nodeValue)
      ) {
        node.removeChild(child);
      } else if (child.nodeType === 1) {
        DOMNormalizer.removeWhitespaces(child as HTMLElement);
      }
    }

    return node;
  }

  private static normalizeBlocks(node: HTMLElement): void {
    const blocks = Object.values(EditorSchema.nodes).filter((node) => node.isBlock);

    const selector = blocks
      .flatMap((block) => block.spec.parseDOM?.map((spec) => spec.tag))
      .filter(Boolean)
      .join(',');

    if (!selector) {
      return;
    }

    [...node.querySelectorAll<HTMLElement>(selector)].forEach((el) => {
      if (el.parentElement?.matches('p')) {
        extractElement(el);
      }
    });
  }

  private static normalizeCodeBlocks(node: HTMLElement): void {
    node.innerHTML = node.innerHTML.replace(/\n<\/code><\/pre>/g, '</code></pre>');
  }

  private static normalizeTaskLists(node: HTMLElement): void {
    node.querySelectorAll('.contains-task-list').forEach((el) => {
      el.setAttribute('data-type', 'taskList');
    });

    node.querySelectorAll('.task-list-item').forEach((item) => {
      const input = item.querySelector('input');
      item.setAttribute('data-type', 'taskItem');
      if (input) {
        item.setAttribute('data-checked', input.checked.toString());
        input.remove();
      }
    });
  }

  private static removeTrailingNewlines(node: HTMLElement): void {
    node.querySelectorAll('*').forEach((el) => {
      if (el.nextSibling?.nodeType === NODE_TEXT_NODE && !el.closest('pre')) {
        el.nextSibling.textContent = el.nextSibling.textContent?.replace(/^\n/, '') ?? '';
      }
    });
  }

  static normalize(node: HTMLElement): HTMLElement {
    const element = DOMNormalizer.removeWhitespaces(node);

    DOMNormalizer.normalizeBlocks(element);
    DOMNormalizer.normalizeCodeBlocks(element);
    DOMNormalizer.normalizeTaskLists(element);
    DOMNormalizer.removeTrailingNewlines(element);

    return element;
  }
}
