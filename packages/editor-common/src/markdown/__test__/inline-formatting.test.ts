import { MarkdownParse } from '../markdown-parse';

// Mock dependencies
jest.mock('@tiptap/core', () => ({
  elementFromString: jest.fn((content: string) => {
    const parser = new DOMParser();
    return parser.parseFromString(`<body>${content}</body>`, 'text/html').body;
  }),
}));

jest.mock('../../extensions', () => ({
  EditorSchema: {
    nodes: {
      paragraph: { isBlock: true, spec: { parseDOM: [{ tag: 'p' }] } },
      heading: { isBlock: true, spec: { parseDOM: [{ tag: 'h1' }, { tag: 'h2' }] } },
      blockquote: { isBlock: true, spec: { parseDOM: [{ tag: 'blockquote' }] } },
    },
  },
}));

describe('MarkdownParse - Inline Formatting', () => {
  let markdownParse: MarkdownParse;

  beforeEach(() => {
    markdownParse = new MarkdownParse();
  });

  describe('Bold Text', () => {
    it('should handle double asterisks bold', () => {
      const content = '**bold text**';
      const result = markdownParse.parse(content);
      expect(result).toContain('<strong>bold text</strong>');
    });

    it('should handle double underscores bold', () => {
      const content = '__bold text__';
      const result = markdownParse.parse(content);
      expect(result).toContain('<strong>bold text</strong>');
    });

    it('should handle multiple bold texts', () => {
      const content = '**first** and **second** bold text';
      const result = markdownParse.parse(content);
      expect(result).toContain('<strong>first</strong>');
      expect(result).toContain('<strong>second</strong>');
    });

    it('should handle bold with punctuation', () => {
      const content = '**bold!** **bold?** **bold.**';
      const result = markdownParse.parse(content);
      expect(result).toContain('<strong>bold!</strong>');
      expect(result).toContain('<strong>bold?</strong>');
      expect(result).toContain('<strong>bold.</strong>');
    });

    it('should handle bold across word boundaries', () => {
      const content = 'This **is bold** text';
      const result = markdownParse.parse(content);
      expect(result).toContain('This <strong>is bold</strong> text');
    });

    it('should handle nested formatting in bold', () => {
      const content = '**bold with *italic* inside**';
      const result = markdownParse.parse(content);
      expect(result).toContain('<strong>bold with <em>italic</em> inside</strong>');
    });

    it('should handle bold with special characters', () => {
      const content = '**bold & special <> chars**';
      const result = markdownParse.parse(content);
      expect(result).toContain('<strong>bold &amp; special &lt;&gt; chars</strong>');
    });

    it('should handle unclosed bold gracefully', () => {
      const content = '**unclosed bold text';
      const result = markdownParse.parse(content);
      expect(result).toBeDefined();
      expect(result).toContain('unclosed bold text');
    });
  });

  describe('Italic Text', () => {
    it('should handle single asterisk italic', () => {
      const content = '*italic text*';
      const result = markdownParse.parse(content);
      expect(result).toContain('<em>italic text</em>');
    });

    it('should handle single underscore italic', () => {
      const content = '_italic text_';
      const result = markdownParse.parse(content);
      expect(result).toContain('<em>italic text</em>');
    });

    it('should handle multiple italic texts', () => {
      const content = '*first* and *second* italic text';
      const result = markdownParse.parse(content);
      expect(result).toContain('<em>first</em>');
      expect(result).toContain('<em>second</em>');
    });

    it('should handle italic with punctuation', () => {
      const content = '*italic!* *italic?* *italic.*';
      const result = markdownParse.parse(content);
      expect(result).toContain('<em>italic!</em>');
      expect(result).toContain('<em>italic?</em>');
      expect(result).toContain('<em>italic.</em>');
    });

    it('should handle italic across word boundaries', () => {
      const content = 'This *is italic* text';
      const result = markdownParse.parse(content);
      expect(result).toContain('This <em>is italic</em> text');
    });

    it('should handle nested formatting in italic', () => {
      const content = '*italic with **bold** inside*';
      const result = markdownParse.parse(content);
      expect(result).toContain('<em>italic with <strong>bold</strong> inside</em>');
    });

    it('should handle unclosed italic gracefully', () => {
      const content = '*unclosed italic text';
      const result = markdownParse.parse(content);
      expect(result).toBeDefined();
      expect(result).toContain('unclosed italic text');
    });
  });

  describe('Bold Italic Combinations', () => {
    it('should handle triple asterisks bold italic', () => {
      const content = '***bold and italic***';
      const result = markdownParse.parse(content);
      expect(result).toContain('<strong><em>bold and italic</em></strong>');
    });

    it('should handle triple underscores bold italic', () => {
      const content = '___bold and italic___';
      const result = markdownParse.parse(content);
      expect(result).toContain('<strong><em>bold and italic</em></strong>');
    });

    it('should handle mixed asterisk underscore combinations', () => {
      const content = '**bold *and italic* text**';
      const result = markdownParse.parse(content);
      expect(result).toContain('<strong>bold <em>and italic</em> text</strong>');
    });

    it('should handle separate bold and italic', () => {
      const content = '**bold** and *italic* separately';
      const result = markdownParse.parse(content);
      expect(result).toContain('<strong>bold</strong>');
      expect(result).toContain('<em>italic</em>');
    });

    it('should handle complex nesting', () => {
      const content = '***bold italic*** **just bold** *just italic*';
      const result = markdownParse.parse(content);
      expect(result).toContain('<strong><em>bold italic</em></strong>');
      expect(result).toContain('<strong>just bold</strong>');
      expect(result).toContain('<em>just italic</em>');
    });
  });

  describe('Strikethrough', () => {
    it('should handle double tilde strikethrough', () => {
      const content = '~~strikethrough text~~';
      const result = markdownParse.parse(content);
      expect(result).toContain('<s>strikethrough text</s>');
    });

    it('should handle multiple strikethrough texts', () => {
      const content = '~~first~~ and ~~second~~ struck';
      const result = markdownParse.parse(content);
      expect(result).toContain('<s>first</s>');
      expect(result).toContain('<s>second</s>');
    });

    it('should handle strikethrough with other formatting', () => {
      const content = '~~strike with **bold** inside~~';
      const result = markdownParse.parse(content);
      expect(result).toContain('<s>strike with <strong>bold</strong> inside</s>');
    });

    it('should handle nested strikethrough', () => {
      const content = '**bold with ~~strike~~ inside**';
      const result = markdownParse.parse(content);
      expect(result).toContain('<strong>bold with <s>strike</s> inside</strong>');
    });

    it('should handle unclosed strikethrough', () => {
      const content = '~~unclosed strike text';
      const result = markdownParse.parse(content);
      expect(result).toBeDefined();
      expect(result).toContain('unclosed strike text');
    });

    it('should handle single tilde (should not work)', () => {
      const content = '~single tilde~';
      const result = markdownParse.parse(content);
      expect(result).not.toContain('<s>');
      expect(result).toContain('~single tilde~');
    });
  });

  describe('Mark/Highlight', () => {
    it('should handle double equals highlight', () => {
      const content = '==highlighted text==';
      const result = markdownParse.parse(content);
      expect(result).toContain('<mark>highlighted text</mark>');
    });

    it('should handle multiple highlights', () => {
      const content = '==first== and ==second== highlighted';
      const result = markdownParse.parse(content);
      expect(result).toContain('<mark>first</mark>');
      expect(result).toContain('<mark>second</mark>');
    });

    it('should handle highlight with other formatting', () => {
      const content = '==highlight with **bold** inside==';
      const result = markdownParse.parse(content);
      expect(result).toContain('<mark>highlight with <strong>bold</strong> inside</mark>');
    });

    it('should handle nested highlight', () => {
      const content = '**bold with ==highlight== inside**';
      const result = markdownParse.parse(content);
      expect(result).toContain('<strong>bold with <mark>highlight</mark> inside</strong>');
    });

    it('should handle unclosed highlight', () => {
      const content = '==unclosed highlight text';
      const result = markdownParse.parse(content);
      expect(result).toBeDefined();
      expect(result).toContain('unclosed highlight text');
    });
  });

  describe('Inline Code', () => {
    it('should handle single backtick code', () => {
      const content = '`inline code`';
      const result = markdownParse.parse(content);
      expect(result).toContain('<code>inline code</code>');
    });

    it('should handle multiple inline codes', () => {
      const content = '`first` and `second` code';
      const result = markdownParse.parse(content);
      expect(result).toContain('<code>first</code>');
      expect(result).toContain('<code>second</code>');
    });

    it('should preserve special characters in code', () => {
      const content = '`<script>alert("xss")</script>`';
      const result = markdownParse.parse(content);
      expect(result).toContain('<code>&lt;script&gt;alert(&quot;xss&quot;)&lt;/script&gt;</code>');
    });

    it('should handle code with formatting markers inside', () => {
      const content = '`**not bold** *not italic*`';
      const result = markdownParse.parse(content);
      expect(result).toContain('<code>**not bold** *not italic*</code>');
      expect(result).not.toContain('<strong>');
      expect(result).not.toContain('<em>');
    });

    it('should handle code in sentences', () => {
      const content = 'Use `console.log()` to debug';
      const result = markdownParse.parse(content);
      expect(result).toContain('Use <code>console.log()</code> to debug');
    });

    it('should handle empty code', () => {
      const content = 'Empty code: ``';
      const result = markdownParse.parse(content);
      expect(result).toContain('<code></code>');
    });

    it('should handle code with spaces', () => {
      const content = '` spaced code `';
      const result = markdownParse.parse(content);
      expect(result).toContain('<code> spaced code </code>');
    });
  });

  describe('Underline', () => {
    it('should handle plus plus underline', () => {
      const content = '++underlined text++';
      const result = markdownParse.parse(content);
      // Note: This might not work depending on plugin configuration
      expect(result).toContain('underlined text');
    });

    it('should handle underline with other formatting', () => {
      const content = '++underline with **bold** inside++';
      const result = markdownParse.parse(content);
      expect(result).toContain('underline');
      expect(result).toContain('bold');
    });
  });

  describe('Complex Inline Formatting Combinations', () => {
    it('should handle all formatting types together', () => {
      const content = '**Bold** *italic* ~~strike~~ ==highlight== `code`';
      const result = markdownParse.parse(content);
      expect(result).toContain('<strong>Bold</strong>');
      expect(result).toContain('<em>italic</em>');
      expect(result).toContain('<s>strike</s>');
      expect(result).toContain('<mark>highlight</mark>');
      expect(result).toContain('<code>code</code>');
    });

    it('should handle deeply nested formatting', () => {
      const content = '**bold with *italic and ~~strike with ==highlight==~~ text* inside**';
      const result = markdownParse.parse(content);
      expect(result).toContain('<strong>');
      expect(result).toContain('<em>');
      expect(result).toContain('<s>');
      expect(result).toContain('<mark>');
    });

    it('should handle overlapping formatting', () => {
      const content = '**bold *italic overlap** still italic*';
      const result = markdownParse.parse(content);
      expect(result).toBeDefined();
      // Behavior may vary based on markdown-it parser
    });

    it('should handle adjacent formatting', () => {
      const content = '**bold**_italic_~~strike~~==highlight==`code`';
      const result = markdownParse.parse(content);
      expect(result).toContain('<strong>bold</strong>');
      expect(result).toContain('<em>italic</em>');
      expect(result).toContain('<s>strike</s>');
      expect(result).toContain('<mark>highlight</mark>');
      expect(result).toContain('<code>code</code>');
    });

    it('should handle formatting with punctuation', () => {
      const content = '**Bold!** *Italic?* ~~Strike.~~ ==Highlight,== `code;`';
      const result = markdownParse.parse(content);
      expect(result).toContain('<strong>Bold!</strong>');
      expect(result).toContain('<em>Italic?</em>');
      expect(result).toContain('<s>Strike.</s>');
      expect(result).toContain('<mark>Highlight,</mark>');
      expect(result).toContain('<code>code;</code>');
    });

    it('should handle formatting across multiple lines', () => {
      const content = '**This is bold\nacross lines**';
      const result = markdownParse.parse(content);
      expect(result).toContain('<strong>This is bold\nacross lines</strong>');
    });

    it('should handle formatting with special characters', () => {
      const content = '**Bold & <special>** *Italic & <chars>*';
      const result = markdownParse.parse(content);
      expect(result).toContain('<strong>Bold &amp; &lt;special&gt;</strong>');
      expect(result).toContain('<em>Italic &amp; &lt;chars&gt;</em>');
    });

    it('should handle formatting with numbers and symbols', () => {
      const content = '**2023** *$100* ~~€50~~ ==@user== `#hashtag`';
      const result = markdownParse.parse(content);
      expect(result).toContain('<strong>2023</strong>');
      expect(result).toContain('<em>$100</em>');
      expect(result).toContain('<s>€50</s>');
      expect(result).toContain('<mark>@user</mark>');
      expect(result).toContain('<code>#hashtag</code>');
    });
  });

  describe('Edge Cases and Malformed Input', () => {
    it('should handle mismatched delimiters', () => {
      const malformedCases = [
        '**bold but _italic end_',
        '*italic but **bold end**',
        '~~strike but ==highlight end==',
        '==highlight but `code end`',
      ];

      malformedCases.forEach(content => {
        const result = markdownParse.parse(content);
        expect(result).toBeDefined();
        expect(result.length).toBeGreaterThan(0);
      });
    });

    it('should handle escaped characters', () => {
      const content = '\\**not bold\\** \\*not italic\\* \\~~not strike\\~~';
      const result = markdownParse.parse(content);
      expect(result).not.toContain('<strong>');
      expect(result).not.toContain('<em>');
      expect(result).not.toContain('<s>');
      expect(result).toContain('**not bold**');
      expect(result).toContain('*not italic*');
    });

    it('should handle very long formatted text', () => {
      const longText = 'word '.repeat(1000);
      const content = `**${longText}**`;
      const result = markdownParse.parse(content);
      expect(result).toContain('<strong>');
      expect(result).toContain(longText.trim());
    });

    it('should handle empty formatting', () => {
      const content = '** ** * * ~~ ~~ == == ` `';
      const result = markdownParse.parse(content);
      expect(result).toBeDefined();
    });

    it('should handle formatting at text boundaries', () => {
      const content = '**start** middle **end**';
      const result = markdownParse.parse(content);
      expect(result).toContain('<strong>start</strong>');
      expect(result).toContain('<strong>end</strong>');
      expect(result).toContain(' middle ');
    });

    it('should handle multiple spaces in formatting', () => {
      const content = '**bold   with   spaces** *italic   spaces*';
      const result = markdownParse.parse(content);
      expect(result).toContain('<strong>bold   with   spaces</strong>');
      expect(result).toContain('<em>italic   spaces</em>');
    });

    it('should handle unicode in formatting', () => {
      const content = '**粗体** *斜体* ~~删除线~~ ==高亮== `代码`';
      const result = markdownParse.parse(content);
      expect(result).toContain('<strong>粗体</strong>');
      expect(result).toContain('<em>斜体</em>');
      expect(result).toContain('<s>删除线</s>');
      expect(result).toContain('<mark>高亮</mark>');
      expect(result).toContain('<code>代码</code>');
    });

    it('should handle emoji in formatting', () => {
      const content = '**🎉 Bold emoji** *😊 Italic emoji* ~~❌ Strike emoji~~';
      const result = markdownParse.parse(content);
      expect(result).toContain('<strong>🎉 Bold emoji</strong>');
      expect(result).toContain('<em>😊 Italic emoji</em>');
      expect(result).toContain('<s>❌ Strike emoji</s>');
    });
  });

  describe('Performance Tests', () => {
    it('should handle many formatted elements efficiently', () => {
      const elements = Array.from({ length: 100 }, (_, i) => 
        `**Bold ${i}** *Italic ${i}* ~~Strike ${i}~~ ==Mark ${i}== \`code${i}\``
      ).join(' ');
      
      const result = markdownParse.parse(elements);
      expect(result).toContain('<strong>Bold 0</strong>');
      expect(result).toContain('<strong>Bold 99</strong>');
      expect((result.match(/<strong>/g) || []).length).toBe(100);
      expect((result.match(/<em>/g) || []).length).toBe(100);
    });

    it('should handle complex nested structures efficiently', () => {
      let content = 'Text ';
      for (let i = 0; i < 50; i++) {
        content += `**bold ${i} with *italic ${i}* inside** `;
      }
      
      const result = markdownParse.parse(content);
      expect(result).toContain('<strong>bold 0');
      expect(result).toContain('<strong>bold 49');
      expect(result).toContain('<em>italic 0</em>');
      expect(result).toContain('<em>italic 49</em>');
    });
  });
});