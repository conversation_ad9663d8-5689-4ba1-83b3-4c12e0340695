import { MarkdownParse } from '../markdown-parse';

// Mock dependencies
jest.mock('@tiptap/core', () => ({
  elementFromString: jest.fn((content: string) => {
    const parser = new DOMParser();
    return parser.parseFromString(`<body>${content}</body>`, 'text/html').body;
  }),
}));

jest.mock('../../extensions', () => ({
  EditorSchema: {
    nodes: {
      paragraph: { isBlock: true, spec: { parseDOM: [{ tag: 'p' }] } },
      heading: { isBlock: true, spec: { parseDOM: [{ tag: 'h1' }, { tag: 'h2' }] } },
      blockquote: { isBlock: true, spec: { parseDOM: [{ tag: 'blockquote' }] } },
    },
  },
}));

describe('MarkdownParse - Links', () => {
  let markdownParse: MarkdownParse;

  beforeEach(() => {
    markdownParse = new MarkdownParse();
  });

  describe('Inline Links', () => {
    it('should handle basic inline links', () => {
      const content = '[Link text](https://example.com)';
      const result = markdownParse.parse(content);
      expect(result).toContain('<a href="https://example.com">Link text</a>');
    });

    it('should handle links with titles', () => {
      const content = '[Link](https://example.com "Title text")';
      const result = markdownParse.parse(content);
      expect(result).toContain('<a href="https://example.com" title="Title text">Link</a>');
    });

    it('should handle links with single quote titles', () => {
      const content = "[Link](https://example.com 'Title text')";
      const result = markdownParse.parse(content);
      expect(result).toContain('title="Title text"');
    });

    it('should handle multiple links in one line', () => {
      const content = '[First](https://first.com) and [Second](https://second.com)';
      const result = markdownParse.parse(content);
      expect(result).toContain('<a href="https://first.com">First</a>');
      expect(result).toContain('<a href="https://second.com">Second</a>');
    });

    it('should handle links with special characters in URL', () => {
      const specialUrls = [
        'https://example.com/path?query=value&other=123',
        'https://user:<EMAIL>:8080/path',
        'https://example.com/path#fragment',
        'https://example.com/path%20with%20spaces',
      ];

      specialUrls.forEach(url => {
        const content = `[Link](${url})`;
        const result = markdownParse.parse(content);
        expect(result).toContain(`href="${url}"`);
      });
    });

    it('should handle links with formatted text', () => {
      const content = '[**Bold link**](https://example.com)';
      const result = markdownParse.parse(content);
      expect(result).toContain('<a href="https://example.com"><strong>Bold link</strong></a>');
    });

    it('should handle links with multiple formatting', () => {
      const content = '[**Bold** *italic* `code`](https://example.com)';
      const result = markdownParse.parse(content);
      expect(result).toContain('<a href="https://example.com">');
      expect(result).toContain('<strong>Bold</strong>');
      expect(result).toContain('<em>italic</em>');
      expect(result).toContain('<code>code</code>');
    });

    it('should handle empty link text', () => {
      const content = '[](https://example.com)';
      const result = markdownParse.parse(content);
      expect(result).toContain('<a href="https://example.com"></a>');
    });

    it('should handle links with parentheses in URL', () => {
      const content = '[Link](https://example.com/page(1))';
      const result = markdownParse.parse(content);
      expect(result).toContain('href="https://example.com/page(1)"');
    });

    it('should handle malformed links gracefully', () => {
      const malformedLinks = [
        '[Link](https://example.com',  // Missing closing parenthesis
        '[Link(https://example.com)',  // Missing closing bracket
        'Link](https://example.com)',  // Missing opening bracket
        '[Link]()',                    // Empty URL
      ];

      malformedLinks.forEach(content => {
        const result = markdownParse.parse(content);
        expect(result).toBeDefined();
        // Should handle gracefully without throwing errors
      });
    });
  });

  describe('Reference Links', () => {
    it('should handle basic reference links', () => {
      const content = `[Link text][1]

[1]: https://example.com`;
      const result = markdownParse.parse(content);
      expect(result).toContain('<a href="https://example.com">Link text</a>');
    });

    it('should handle reference links with titles', () => {
      const content = `[Link][ref]

[ref]: https://example.com "Reference title"`;
      const result = markdownParse.parse(content);
      expect(result).toContain('<a href="https://example.com" title="Reference title">Link</a>');
    });

    it('should handle multiple reference links', () => {
      const content = `[First][1] and [Second][2]

[1]: https://first.com
[2]: https://second.com`;
      const result = markdownParse.parse(content);
      expect(result).toContain('<a href="https://first.com">First</a>');
      expect(result).toContain('<a href="https://second.com">Second</a>');
    });

    it('should handle shortcut reference links', () => {
      const content = `[Google][]

[Google]: https://google.com`;
      const result = markdownParse.parse(content);
      expect(result).toContain('<a href="https://google.com">Google</a>');
    });

    it('should handle implicit reference links', () => {
      const content = `[Google]

[Google]: https://google.com`;
      const result = markdownParse.parse(content);
      expect(result).toContain('<a href="https://google.com">Google</a>');
    });

    it('should handle reference links with complex labels', () => {
      const content = `[Complex Label 123][complex-label]

[complex-label]: https://example.com`;
      const result = markdownParse.parse(content);
      expect(result).toContain('<a href="https://example.com">Complex Label 123</a>');
    });

    it('should handle case-insensitive reference labels', () => {
      const content = `[Link][REF]

[ref]: https://example.com`;
      const result = markdownParse.parse(content);
      expect(result).toContain('<a href="https://example.com">Link</a>');
    });

    it('should handle reference definitions anywhere in document', () => {
      const content = `[ref]: https://example.com

Text with [reference link][ref] here.

More text.`;
      const result = markdownParse.parse(content);
      expect(result).toContain('<a href="https://example.com">reference link</a>');
    });
  });

  describe('Automatic Links', () => {
    it('should handle HTTP URLs', () => {
      const content = 'Visit https://example.com for more info';
      const result = markdownParse.parse(content);
      expect(result).toContain('<a href="https://example.com">https://example.com</a>');
    });

    it('should handle HTTP URLs without HTTPS', () => {
      const content = 'Visit http://example.com for more info';
      const result = markdownParse.parse(content);
      expect(result).toContain('<a href="http://example.com">http://example.com</a>');
    });

    it('should handle FTP URLs', () => {
      const content = 'Download from ftp://files.example.com';
      const result = markdownParse.parse(content);
      expect(result).toContain('<a href="ftp://files.example.com">ftp://files.example.com</a>');
    });

    it('should handle email addresses', () => {
      const content = 'Contact <NAME_EMAIL>';
      const result = markdownParse.parse(content);
      expect(result).toContain('<a href="mailto:<EMAIL>"><EMAIL></a>');
    });

    it('should handle complex email addresses', () => {
      const emails = [
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
      ];

      emails.forEach(email => {
        const content = `Email: ${email}`;
        const result = markdownParse.parse(content);
        expect(result).toContain(`<a href="mailto:${email}">${email}</a>`);
      });
    });

    it('should handle URLs with query parameters', () => {
      const content = 'Search: https://google.com/search?q=test&lang=en';
      const result = markdownParse.parse(content);
      expect(result).toContain('<a href="https://google.com/search?q=test&lang=en">');
    });

    it('should handle URLs with fragments', () => {
      const content = 'See https://example.com/page#section1';
      const result = markdownParse.parse(content);
      expect(result).toContain('<a href="https://example.com/page#section1">');
    });

    it('should handle URLs in parentheses', () => {
      const content = 'See the website (https://example.com) for details';
      const result = markdownParse.parse(content);
      expect(result).toContain('<a href="https://example.com">https://example.com</a>');
    });
  });

  describe('Angle Bracket Links', () => {
    it('should handle angle bracket URLs', () => {
      const content = '<https://example.com>';
      const result = markdownParse.parse(content);
      expect(result).toContain('<a href="https://example.com">https://example.com</a>');
    });

    it('should handle angle bracket emails', () => {
      const content = '<<EMAIL>>';
      const result = markdownParse.parse(content);
      expect(result).toContain('<a href="mailto:<EMAIL>"><EMAIL></a>');
    });

    it('should handle multiple angle bracket links', () => {
      const content = '<https://first.com> and <<EMAIL>>';
      const result = markdownParse.parse(content);
      expect(result).toContain('<a href="https://first.com">https://first.com</a>');
      expect(result).toContain('<a href="mailto:<EMAIL>"><EMAIL></a>');
    });
  });

  describe('Complex Link Scenarios', () => {
    it('should handle links in headings', () => {
      const content = '# Title with [Link](https://example.com)';
      const result = markdownParse.parse(content);
      expect(result).toContain('<h1>Title with <a href="https://example.com">Link</a></h1>');
    });

    it('should handle links in lists', () => {
      const content = `- [First link](https://first.com)
- [Second link](https://second.com)
  - [Nested link](https://nested.com)`;
      const result = markdownParse.parse(content);
      expect(result).toContain('<ul>');
      expect(result).toContain('<a href="https://first.com">First link</a>');
      expect(result).toContain('<a href="https://nested.com">Nested link</a>');
    });

    it('should handle links in blockquotes', () => {
      const content = '> Quote with [link](https://example.com) inside';
      const result = markdownParse.parse(content);
      expect(result).toContain('<blockquote>');
      expect(result).toContain('<a href="https://example.com">link</a>');
    });

    it('should handle links in tables', () => {
      const content = `| Site | Link |
|------|------|
| Google | [Visit](https://google.com) |
| GitHub | [Code](https://github.com) |`;
      const result = markdownParse.parse(content);
      expect(result).toContain('<table>');
      expect(result).toContain('<a href="https://google.com">Visit</a>');
      expect(result).toContain('<a href="https://github.com">Code</a>');
    });

    it('should handle links with images', () => {
      const content = '[Click ![icon](icon.png) here](https://example.com)';
      const result = markdownParse.parse(content);
      expect(result).toContain('<a href="https://example.com">');
      expect(result).toContain('src="icon.png"');
    });

    it('should handle nested links and formatting', () => {
      const content = '**Bold text with [link](https://example.com) and *italic***';
      const result = markdownParse.parse(content);
      expect(result).toContain('<strong>');
      expect(result).toContain('<a href="https://example.com">link</a>');
      expect(result).toContain('<em>italic</em>');
    });
  });

  describe('International and Special URLs', () => {
    it('should handle international domain names', () => {
      const content = '[中文](https://测试.中国) and [русский](https://тест.рф)';
      const result = markdownParse.parse(content);
      expect(result).toContain('href="https://测试.中国"');
      expect(result).toContain('href="https://тест.рф"');
    });

    it('should handle URLs with various protocols', () => {
      const protocols = [
        'https://example.com',
        'http://example.com',
        'ftp://example.com',
        'ssh://example.com',
        'git://example.com',
      ];

      protocols.forEach(url => {
        const content = `[Protocol link](${url})`;
        const result = markdownParse.parse(content);
        expect(result).toContain(`href="${url}"`);
      });
    });

    it('should handle relative URLs', () => {
      const relativeUrls = [
        '/absolute/path',
        '../relative/path',
        './current/path',
        'relative-file.html',
        '#fragment-only',
      ];

      relativeUrls.forEach(url => {
        const content = `[Relative](${url})`;
        const result = markdownParse.parse(content);
        expect(result).toContain(`href="${url}"`);
      });
    });

    it('should handle data URLs', () => {
      const dataUrl = 'data:text/plain;base64,SGVsbG8gV29ybGQ=';
      const content = `[Data link](${dataUrl})`;
      const result = markdownParse.parse(content);
      expect(result).toContain(`href="${dataUrl}"`);
    });

    it('should handle javascript URLs (security test)', () => {
      const jsUrl = 'javascript:alert("xss")';
      const content = `[JS link](${jsUrl})`;
      const result = markdownParse.parse(content);
      // Should include the URL as-is (security filtering should be done at rendering level)
      expect(result).toContain(`href="${jsUrl}"`);
    });
  });

  describe('Link Edge Cases', () => {
    it('should handle very long URLs', () => {
      const longPath = 'very-long-path-segment/'.repeat(50);
      const longUrl = `https://example.com/${longPath}page.html`;
      const content = `[Long URL](${longUrl})`;
      const result = markdownParse.parse(content);
      expect(result).toContain(`href="${longUrl}"`);
    });

    it('should handle URLs with special characters', () => {
      const specialChars = "!*'();:@&=+$,/?#[]";
      const encodedUrl = `https://example.com/path?special=${encodeURIComponent(specialChars)}`;
      const content = `[Special chars](${encodedUrl})`;
      const result = markdownParse.parse(content);
      expect(result).toContain('href="');
    });

    it('should handle links with line breaks in text', () => {
      const content = `[Link
with line break](https://example.com)`;
      const result = markdownParse.parse(content);
      expect(result).toContain('<a href="https://example.com">');
      expect(result).toContain('Link\nwith line break');
    });

    it('should handle escaped link characters', () => {
      const content = '\\[Not a link\\](https://example.com)';
      const result = markdownParse.parse(content);
      expect(result).not.toContain('<a href');
      expect(result).toContain('[Not a link](https://example.com)');
    });

    it('should handle mixed link types in same document', () => {
      const content = `[Inline](https://inline.com)
[Reference][ref]
<https://angle.com>
https://auto.com
<EMAIL>

[ref]: https://reference.com`;
      const result = markdownParse.parse(content);
      expect(result).toContain('<a href="https://inline.com">Inline</a>');
      expect(result).toContain('<a href="https://reference.com">Reference</a>');
      expect(result).toContain('<a href="https://angle.com">https://angle.com</a>');
      expect(result).toContain('<a href="https://auto.com">https://auto.com</a>');
      expect(result).toContain('<a href="mailto:<EMAIL>"><EMAIL></a>');
    });
  });

  describe('Performance Tests', () => {
    it('should handle many links efficiently', () => {
      const links = Array.from({ length: 100 }, (_, i) => 
        `[Link ${i}](https://example${i}.com)`
      ).join(' ');
      
      const result = markdownParse.parse(links);
      expect(result).toContain('<a href="https://example0.com">Link 0</a>');
      expect(result).toContain('<a href="https://example99.com">Link 99</a>');
      expect((result.match(/<a href=/g) || []).length).toBe(100);
    });

    it('should handle complex link structures', () => {
      let content = '';
      for (let i = 0; i < 20; i++) {
        content += `## Section ${i}
        
[Link ${i}](https://example${i}.com "Title ${i}")

- [List link ${i}](https://list${i}.com)
- Another item

> Quote with [quoted link ${i}](https://quote${i}.com)

`;
      }
      
      const result = markdownParse.parse(content);
      expect(result).toContain('<h2>Section 0</h2>');
      expect(result).toContain('<h2>Section 19</h2>');
      expect(result).toContain('<a href="https://example0.com"');
      expect(result).toContain('<a href="https://quote19.com"');
      expect((result.match(/<a href=/g) || []).length).toBeGreaterThanOrEqual(40);
    });
  });
});