import { MarkdownParse } from '../markdown-parse';
import { EMPTY_PARAGRAPH_NODE_MARKDOWN_VAL } from '../const';

// Mock dependencies
jest.mock('@tiptap/core', () => ({
  elementFromString: jest.fn((content: string) => {
    const parser = new DOMParser();
    return parser.parseFromString(`<body>${content}</body>`, 'text/html').body;
  }),
}));

jest.mock('../../extensions', () => ({
  EditorSchema: {
    nodes: {
      paragraph: { isBlock: true, spec: { parseDOM: [{ tag: 'p' }] } },
      heading: { isBlock: true, spec: { parseDOM: [{ tag: 'h1' }, { tag: 'h2' }] } },
      blockquote: { isBlock: true, spec: { parseDOM: [{ tag: 'blockquote' }] } },
    },
  },
}));

describe('MarkdownParse - Edge Cases and Error Handling', () => {
  let markdownParse: MarkdownParse;

  beforeEach(() => {
    markdownParse = new MarkdownParse();
  });

  describe('Empty and Whitespace Content', () => {
    it('should handle completely empty content', () => {
      const result = markdownParse.parse('');
      expect(result.trim()).toBe('');
    });

    it('should handle whitespace-only content', () => {
      const whitespaceTypes = [
        '   ',
        '\t\t\t',
        '\n\n\n',
        ' \t \n \t ',
        '                    ',
      ];

      whitespaceTypes.forEach(content => {
        const result = markdownParse.parse(content);
        expect(result.trim()).toBe('');
      });
    });

    it('should handle mixed empty lines and whitespace', () => {
      const content = `

    

\t\t

      
`;
      const result = markdownParse.parse(content);
      expect(result.trim()).toBe('');
    });

    it('should handle empty paragraphs correctly', () => {
      const content = EMPTY_PARAGRAPH_NODE_MARKDOWN_VAL;
      const result = markdownParse.parse(content);
      expect(result.trim()).toBe('<p></p>');
    });

    it('should handle multiple empty paragraphs', () => {
      const content = `${EMPTY_PARAGRAPH_NODE_MARKDOWN_VAL}

${EMPTY_PARAGRAPH_NODE_MARKDOWN_VAL}

${EMPTY_PARAGRAPH_NODE_MARKDOWN_VAL}`;
      const result = markdownParse.parse(content);
      const emptyParagraphs = (result.match(/<p><\/p>/g) || []).length;
      expect(emptyParagraphs).toBe(3);
    });
  });

  describe('Malformed Markdown Syntax', () => {
    it('should handle unclosed formatting gracefully', () => {
      const malformedCases = [
        '**Unclosed bold',
        '*Unclosed italic',
        '~~Unclosed strikethrough',
        '==Unclosed highlight',
        '`Unclosed code',
        '[Unclosed link](',
        '![Unclosed image](',
      ];

      malformedCases.forEach(content => {
        const result = markdownParse.parse(content);
        expect(result).toBeDefined();
        expect(result.length).toBeGreaterThan(0);
        expect(result).toContain('Unclosed');
      });
    });

    it('should handle mismatched brackets and parentheses', () => {
      const mismatchedCases = [
        '[Link text}(https://example.com)',
        '[Link text](https://example.com]',
        '![Image alt}(image.jpg)',
        '![Image alt](image.jpg]',
        '**Bold text*',
        '*Italic text**',
        '~~Strike text~',
        '~Strike text~~',
      ];

      mismatchedCases.forEach(content => {
        const result = markdownParse.parse(content);
        expect(result).toBeDefined();
        expect(result.length).toBeGreaterThan(0);
      });
    });

    it('should handle invalid heading syntax', () => {
      const invalidHeadings = [
        '#No space after hash',
        '# # Double hash',
        '#######Too many hashes',
        'Not a heading#',
        '## \t\n Empty heading',
      ];

      invalidHeadings.forEach(content => {
        const result = markdownParse.parse(content);
        expect(result).toBeDefined();
      });
    });

    it('should handle invalid list syntax', () => {
      const invalidLists = [
        '-No space after dash',
        '1.No space after number',
        '- [ ]No space after checkbox',
        '* \t\n Empty item',
        '1. \n\n Empty numbered item',
      ];

      invalidLists.forEach(content => {
        const result = markdownParse.parse(content);
        expect(result).toBeDefined();
      });
    });

    it('should handle invalid table syntax', () => {
      const invalidTables = [
        '| Header |\n| Cell |', // Missing separator
        '| Header | Header 2 |\n|-----|\n| Cell |', // Mismatched columns
        '| Header |\n||\n| Cell |', // Empty separator
        '| Header\n|------|\n| Cell |', // Missing pipes
      ];

      invalidTables.forEach(content => {
        const result = markdownParse.parse(content);
        expect(result).toBeDefined();
      });
    });
  });

  describe('Special Characters and Encoding', () => {
    it('should handle HTML entities correctly', () => {
      const content = '&lt;script&gt; &amp; &quot;quotes&quot; &apos;apostrophe&apos;';
      const result = markdownParse.parse(content);
      expect(result).toContain('&lt;script&gt;');
      expect(result).toContain('&amp;');
      expect(result).toContain('&quot;');
    });

    it('should handle Unicode characters', () => {
      const unicodeTexts = [
        '中文测试 Chinese text',
        'Русский текст Russian text',
        'العربية Arabic text',
        'हिन्दी Hindi text',
        'Español Spanish text with ñ',
        'Français French text with é à ç',
        'Deutsch German text with ß ü ö ä',
      ];

      unicodeTexts.forEach(content => {
        const result = markdownParse.parse(content);
        expect(result).toContain(content);
      });
    });

    it('should handle emoji and symbols', () => {
      const emojiContent = '🎉 Celebration 😊 Happy 🚀 Rocket ⭐ Star 💻 Computer';
      const result = markdownParse.parse(emojiContent);
      expect(result).toContain('🎉');
      expect(result).toContain('😊');
      expect(result).toContain('🚀');
      expect(result).toContain('⭐');
      expect(result).toContain('💻');
    });

    it('should handle mathematical symbols', () => {
      const mathContent = '∑ ∏ ∫ ∆ ∇ ∞ ≤ ≥ ≠ ≈ α β γ δ ε';
      const result = markdownParse.parse(mathContent);
      expect(result).toContain('∑');
      expect(result).toContain('∏');
      expect(result).toContain('α');
      expect(result).toContain('β');
      expect(result).toContain('∞');
    });

    it('should handle special punctuation', () => {
      const punctuation = '""''‚„…–—‹›«»¡¿§¶†‡•‰′″‴‹›€£¥¢';
      const result = markdownParse.parse(punctuation);
      expect(result).toContain(punctuation);
    });

    it('should handle mixed character sets', () => {
      const mixed = 'English 中文 русский العربية 123 !@# 🎉😊 α∞≠';
      const result = markdownParse.parse(mixed);
      expect(result).toContain('English');
      expect(result).toContain('中文');
      expect(result).toContain('русский');
      expect(result).toContain('🎉');
      expect(result).toContain('α∞≠');
    });
  });

  describe('HTML Injection and Security', () => {
    it('should handle script tags in content', () => {
      const scriptContent = '<script>alert("xss")</script>';
      const result = markdownParse.parse(scriptContent);
      // Since HTML is enabled, script tags should be preserved but escaped in attributes
      expect(result).toContain('<script>');
      expect(result).toContain('alert("xss")');
    });

    it('should handle iframe tags', () => {
      const iframeContent = '<iframe src="https://example.com"></iframe>';
      const result = markdownParse.parse(iframeContent);
      expect(result).toContain('<iframe');
    });

    it('should handle style tags', () => {
      const styleContent = '<style>body { background: red; }</style>';
      const result = markdownParse.parse(styleContent);
      expect(result).toContain('<style>');
    });

    it('should handle event handlers in HTML', () => {
      const eventContent = '<div onclick="alert(1)">Click me</div>';
      const result = markdownParse.parse(eventContent);
      expect(result).toContain('<div');
      expect(result).toContain('onclick');
    });

    it('should handle malformed HTML', () => {
      const malformedHTML = '<div><span>unclosed <p>nested</div> malformed';
      const result = markdownParse.parse(malformedHTML);
      expect(result).toBeDefined();
      expect(result).toContain('malformed');
    });

    it('should handle HTML comments', () => {
      const commentContent = '<!-- This is a comment --> Visible text';
      const result = markdownParse.parse(commentContent);
      expect(result).toContain('<!-- This is a comment -->');
      expect(result).toContain('Visible text');
    });

    it('should handle CDATA sections', () => {
      const cdataContent = '<![CDATA[This is CDATA]]>';
      const result = markdownParse.parse(cdataContent);
      expect(result).toBeDefined();
    });
  });

  describe('Line Break Handling', () => {
    it('should handle literal \\n conversion', () => {
      const content = 'Line 1\\nLine 2\\nLine 3';
      const result = markdownParse.parse(content);
      expect(result).toContain('<br>');
      // Should convert \\n to actual line breaks
    });

    it('should handle actual line breaks', () => {
      const content = 'Line 1\nLine 2\nLine 3';
      const result = markdownParse.parse(content);
      expect(result).toContain('<br>');
    });

    it('should handle mixed line break types', () => {
      const content = 'Line 1\\nLine 2\nLine 3\\nLine 4';
      const result = markdownParse.parse(content);
      expect((result.match(/<br>/g) || []).length).toBeGreaterThanOrEqual(2);
    });

    it('should handle Windows CRLF line endings', () => {
      const content = 'Line 1\r\nLine 2\r\nLine 3';
      const result = markdownParse.parse(content);
      expect(result).toContain('<br>');
    });

    it('should handle Mac CR line endings', () => {
      const content = 'Line 1\rLine 2\rLine 3';
      const result = markdownParse.parse(content);
      expect(result).toBeDefined();
    });

    it('should handle mixed line ending types', () => {
      const content = 'Line 1\nLine 2\r\nLine 3\rLine 4';
      const result = markdownParse.parse(content);
      expect(result).toBeDefined();
    });
  });

  describe('Very Large Content', () => {
    it('should handle very long single lines', () => {
      const longLine = 'word '.repeat(10000);
      const result = markdownParse.parse(longLine);
      expect(result).toContain('word');
      expect(result.length).toBeGreaterThan(longLine.length * 0.8); // Allow for HTML overhead
    });

    it('should handle many short lines', () => {
      const manyLines = Array.from({ length: 1000 }, (_, i) => `Line ${i}`).join('\n');
      const result = markdownParse.parse(manyLines);
      expect(result).toContain('Line 0');
      expect(result).toContain('Line 999');
    });

    it('should handle deeply nested structures', () => {
      let content = '';
      for (let i = 0; i < 50; i++) {
        content += '  '.repeat(i) + `- Level ${i}\n`;
      }
      const result = markdownParse.parse(content);
      expect(result).toContain('Level 0');
      expect(result).toContain('Level 49');
    });

    it('should handle many repeated elements', () => {
      const elements = Array.from({ length: 200 }, (_, i) => 
        `**Bold ${i}** *Italic ${i}* [Link ${i}](url${i}) \`code${i}\``
      ).join('\n\n');
      
      const result = markdownParse.parse(elements);
      expect(result).toContain('Bold 0');
      expect(result).toContain('Italic 199');
      expect(result).toContain('Link 150');
      expect(result).toContain('code199');
    });
  });

  describe('Parser State Edge Cases', () => {
    it('should handle rapid context switches', () => {
      const content = `# **_\`~~==Title==~~\`_**

> **Quote** with *emphasis* and \`code\`
> 
> - [ ] Task in quote
> - [x] Completed task

\`\`\`javascript
// Code block
const x = "**not bold**";
\`\`\`

| **Table** | *Headers* |
|-----------|-----------|
| \`Code\`   | ~~Strike~~ |

- Regular list
  1. Nested ordered
     - [ ] Deep task`;

      const result = markdownParse.parse(content);
      expect(result).toContain('<h1>');
      expect(result).toContain('<blockquote>');
      expect(result).toContain('<pre class="javascript">');
      expect(result).toContain('<table>');
      expect(result).toContain('data-type="taskList"');
      expect(result).toContain('<strong>');
      expect(result).toContain('<em>');
    });

    it('should handle invalid nesting', () => {
      const invalidNesting = `**Bold with *italic and \`code** still bold\` and italic*`;
      const result = markdownParse.parse(invalidNesting);
      expect(result).toBeDefined();
      expect(result).toContain('Bold');
    });

    it('should handle overlapping delimiters', () => {
      const overlapping = '**bold *italic** still italic*';
      const result = markdownParse.parse(overlapping);
      expect(result).toBeDefined();
    });

    it('should handle escaped delimiters in various contexts', () => {
      const escaped = `\\**not bold\\** \\*not italic\\* \\~~not strike\\~~
\\==not mark\\== \\[not link\\] \\![not image\\]
\\\`not code\\\``;
      const result = markdownParse.parse(escaped);
      expect(result).not.toContain('<strong>');
      expect(result).not.toContain('<em>');
      expect(result).not.toContain('<s>');
      expect(result).toContain('**not bold**');
      expect(result).toContain('*not italic*');
    });
  });

  describe('Memory and Performance Edge Cases', () => {
    it('should handle extremely nested blockquotes', () => {
      let content = '';
      for (let i = 0; i < 20; i++) {
        content += '>'.repeat(i + 1) + ` Level ${i + 1} quote\n`;
      }
      const result = markdownParse.parse(content);
      expect(result).toContain('<blockquote>');
      expect(result).toContain('Level 1 quote');
      expect(result).toContain('Level 20 quote');
    });

    it('should handle recursive-like structures', () => {
      const recursiveContent = `
- Item 1
  > Quote in list
  > - List in quote
  >   > Quote in list in quote
  >   > 
  >   > \`\`\`
  >   > code in quote
  >   > \`\`\`
  > 
  > More quote
- Item 2`;
      
      const result = markdownParse.parse(recursiveContent);
      expect(result).toContain('<ul>');
      expect(result).toContain('<blockquote>');
      expect(result).toContain('<pre>');
      expect(result).toContain('code in quote');
    });

    it('should handle circular reference attempts', () => {
      const circularRefs = `[Link 1][2]
[Link 2][1]

[1]: #link2
[2]: #link1`;
      
      const result = markdownParse.parse(circularRefs);
      expect(result).toContain('<a href="#link2">Link 1</a>');
      expect(result).toContain('<a href="#link1">Link 2</a>');
    });
  });

  describe('Error Recovery', () => {
    it('should recover from parser errors gracefully', () => {
      const problematicInputs = [
        null as any,
        undefined as any,
        123 as any,
        [] as any,
        {} as any,
      ];

      problematicInputs.forEach(input => {
        try {
          const result = markdownParse.parse(input);
          expect(result).toBeDefined();
        } catch (error) {
          // Should handle type errors gracefully
          expect(error).toBeDefined();
        }
      });
    });

    it('should handle extremely malformed input', () => {
      const malformed = "***[[[***|||```|||***]]]***\n{{{{####}}}}||||||||\n<><><><><><><><>\n!@#$%^&*()_+{}|:\"<>?\n¿¡™£¢∞§¶•ªº–≠\n™£¢∞§¶•ªº–≠\"\"''…æ…¬˚∆˙©ƒ∂ßåππøœ∑´®†¥¨ˆøπ\"'";

      const result = markdownParse.parse(malformed);
      expect(result).toBeDefined();
      expect(result.length).toBeGreaterThan(0);
    });

    it('should handle binary or non-text data', () => {
      const binaryLike = '\x00\x01\x02\x03\x04\x05\xFF\xFE';
      const result = markdownParse.parse(binaryLike);
      expect(result).toBeDefined();
    });

    it('should handle extremely long URLs', () => {
      const longUrl = 'https://example.com/' + 'very-long-segment/'.repeat(1000) + 'end';
      const content = `[Long link](${longUrl})`;
      const result = markdownParse.parse(content);
      expect(result).toContain('<a href=');
      expect(result).toContain('Long link');
    });
  });
});