import { MarkdownParse } from '../markdown-parse';

// Mock dependencies
jest.mock('@tiptap/core', () => ({
  elementFromString: jest.fn((content: string) => {
    const parser = new DOMParser();
    return parser.parseFromString(`<body>${content}</body>`, 'text/html').body;
  }),
}));

jest.mock('../../extensions', () => ({
  EditorSchema: {
    nodes: {
      paragraph: { isBlock: true, spec: { parseDOM: [{ tag: 'p' }] } },
      heading: { isBlock: true, spec: { parseDOM: [{ tag: 'h1' }, { tag: 'h2' }] } },
      blockquote: { isBlock: true, spec: { parseDOM: [{ tag: 'blockquote' }] } },
    },
  },
}));

describe('MarkdownParse - Tables', () => {
  let markdownParse: MarkdownParse;

  beforeEach(() => {
    markdownParse = new MarkdownParse();
  });

  describe('Basic Tables', () => {
    it('should parse simple tables', () => {
      const content = `| Header 1 | Header 2 | Header 3 |
|----------|----------|----------|
| Cell 1   | Cell 2   | Cell 3   |
| Cell 4   | Cell 5   | Cell 6   |`;
      const result = markdownParse.parse(content);
      expect(result).toContain('<table>');
      expect(result).toContain('<thead>');
      expect(result).toContain('<tbody>');
      expect(result).toContain('<th>Header 1</th>');
      expect(result).toContain('<td>Cell 1</td>');
      expect(result).toContain('<td>Cell 6</td>');
    });

    it('should handle tables without outer pipes', () => {
      const content = `Header 1 | Header 2 | Header 3
---------|----------|----------
Cell 1   | Cell 2   | Cell 3
Cell 4   | Cell 5   | Cell 6`;
      const result = markdownParse.parse(content);
      expect(result).toContain('<table>');
      expect(result).toContain('<th>Header 1</th>');
      expect(result).toContain('<td>Cell 1</td>');
    });

    it('should handle single column tables', () => {
      const content = `| Single Column |
|---------------|
| Row 1         |
| Row 2         |
| Row 3         |`;
      const result = markdownParse.parse(content);
      expect(result).toContain('<table>');
      expect(result).toContain('<th>Single Column</th>');
      expect(result).toContain('<td>Row 1</td>');
      expect(result).toContain('<td>Row 3</td>');
    });

    it('should handle tables with many columns', () => {
      const headers = Array.from({ length: 10 }, (_, i) => `Col ${i + 1}`).join(' | ');
      const separator = Array.from({ length: 10 }, () => '---').join(' | ');
      const row1 = Array.from({ length: 10 }, (_, i) => `Data ${i + 1}`).join(' | ');
      const row2 = Array.from({ length: 10 }, (_, i) => `More ${i + 1}`).join(' | ');
      
      const content = `| ${headers} |
| ${separator} |
| ${row1} |
| ${row2} |`;
      const result = markdownParse.parse(content);
      expect(result).toContain('<table>');
      expect(result).toContain('<th>Col 1</th>');
      expect(result).toContain('<th>Col 10</th>');
      expect(result).toContain('<td>Data 1</td>');
      expect(result).toContain('<td>More 10</td>');
    });

    it('should handle empty cells', () => {
      const content = `| Header 1 | Header 2 | Header 3 |
|----------|----------|----------|
| Cell 1   |          | Cell 3   |
|          | Cell 5   |          |
| Cell 7   |          |          |`;
      const result = markdownParse.parse(content);
      expect(result).toContain('<table>');
      expect(result).toContain('<td>Cell 1</td>');
      expect(result).toContain('<td></td>');
      expect(result).toContain('<td>Cell 7</td>');
    });
  });

  describe('Table Alignment', () => {
    it('should handle left-aligned columns', () => {
      const content = `| Left | Left | Left |
|:-----|:-----|:-----|
| L1   | L2   | L3   |`;
      const result = markdownParse.parse(content);
      expect(result).toContain('<table>');
      expect(result).toMatch(/style="text-align:\s*left"/);
    });

    it('should handle center-aligned columns', () => {
      const content = `| Center | Center | Center |
|:------:|:------:|:------:|
| C1     | C2     | C3     |`;
      const result = markdownParse.parse(content);
      expect(result).toContain('<table>');
      expect(result).toMatch(/style="text-align:\s*center"/);
    });

    it('should handle right-aligned columns', () => {
      const content = `| Right | Right | Right |
|------:|------:|------:|
| R1    | R2    | R3    |`;
      const result = markdownParse.parse(content);
      expect(result).toContain('<table>');
      expect(result).toMatch(/style="text-align:\s*right"/);
    });

    it('should handle mixed alignment', () => {
      const content = `| Left | Center | Right | Default |
|:-----|:------:|------:|---------|
| L1   | C1     | R1    | D1      |
| L2   | C2     | R2    | D2      |`;
      const result = markdownParse.parse(content);
      expect(result).toContain('<table>');
      expect(result).toMatch(/style="text-align:\s*left"/);
      expect(result).toMatch(/style="text-align:\s*center"/);
      expect(result).toMatch(/style="text-align:\s*right"/);
    });

    it('should handle malformed alignment syntax', () => {
      const content = `| Col1 | Col2 | Col3 | Col4 |
|:--   |:----:|---:  |---   |
| A    | B    | C    | D    |`;
      const result = markdownParse.parse(content);
      expect(result).toContain('<table>');
      expect(result).toContain('<td>A</td>');
      expect(result).toContain('<td>D</td>');
    });
  });

  describe('Complex Table Content', () => {
    it('should handle tables with inline formatting', () => {
      const content = `| **Bold** | *Italic* | \`Code\` |
|----------|----------|---------|
| **B1**   | *I1*     | \`C1\`   |
| ~~Strike~~ | ==Mark== | Plain  |`;
      const result = markdownParse.parse(content);
      expect(result).toContain('<table>');
      expect(result).toContain('<th><strong>Bold</strong></th>');
      expect(result).toContain('<th><em>Italic</em></th>');
      expect(result).toContain('<th><code>Code</code></th>');
      expect(result).toContain('<td><strong>B1</strong></td>');
      expect(result).toContain('<td><s>Strike</s></td>');
      expect(result).toContain('<td><mark>Mark</mark></td>');
    });

    it('should handle tables with links and images', () => {
      const content = `| Links | Images | Mixed |
|-------|--------|-------|
| [Google](https://google.com) | ![Icon](icon.png) | [Link ![img](small.jpg)](url) |
| [GitHub](https://github.com) | ![Photo](photo.jpg) | Text and ![inline](inline.png) |`;
      const result = markdownParse.parse(content);
      expect(result).toContain('<table>');
      expect(result).toContain('<a href="https://google.com">Google</a>');
      expect(result).toContain('<a href="https://github.com">GitHub</a>');
      expect(result).toContain('src="icon.png"');
      expect(result).toContain('src="photo.jpg"');
    });

    it('should handle tables with line breaks', () => {
      const content = `| Column 1 | Column 2 |
|----------|----------|
| Line 1<br>Line 2 | Single line |
| Multi<br>Line<br>Cell | Another<br>Break |`;
      const result = markdownParse.parse(content);
      expect(result).toContain('<table>');
      expect(result).toContain('<br>');
      expect(result).toContain('Line 1<br>Line 2');
    });

    it('should handle tables with special characters', () => {
      const content = `| Symbols | Unicode | HTML |
|---------|---------|------|
| !@#$%^&*() | 测试 🚀 | &lt;&gt;&amp; |
| \`\`\`code\`\`\` | αβγδε | &quot;&apos; |
| Pipe \\| Escape | © ® ™ | &nbsp; |`;
      const result = markdownParse.parse(content);
      expect(result).toContain('<table>');
      expect(result).toContain('!@#$%^&*()');
      expect(result).toContain('测试 🚀');
      expect(result).toContain('αβγδε');
      expect(result).toContain('Pipe | Escape');
    });

    it('should handle tables with very long content', () => {
      const longText = 'This is a very long text that should be handled properly in table cells '.repeat(10);
      const content = `| Short | Long Content |
|-------|--------------|
| A     | ${longText} |
| B     | Short again  |`;
      const result = markdownParse.parse(content);
      expect(result).toContain('<table>');
      expect(result).toContain(longText);
      expect(result).toContain('<td>A</td>');
    });

    it('should handle tables with code blocks (should not work but handle gracefully)', () => {
      const content = `| Column 1 | Column 2 |
|----------|----------|
| Normal   | \`\`\`code block\`\`\` |
| Text     | More text |`;
      const result = markdownParse.parse(content);
      expect(result).toContain('<table>');
      expect(result).toContain('Normal');
      // Code blocks inside tables are typically treated as inline code
    });
  });

  describe('Table Edge Cases', () => {
    it('should handle tables with uneven columns', () => {
      const content = `| Col1 | Col2 | Col3 |
|------|------|------|
| A    | B    |
| D    | E    | F    | Extra |
| G    |`;
      const result = markdownParse.parse(content);
      expect(result).toContain('<table>');
      expect(result).toContain('<td>A</td>');
      expect(result).toContain('<td>G</td>');
    });

    it('should handle tables with missing separators', () => {
      const content = `| Col1 | Col2 | Col3 |
| A    | B    | C    |
| D    | E    | F    |`;
      const result = markdownParse.parse(content);
      // Without separator row, this might not be recognized as a table
      expect(result).toBeDefined();
    });

    it('should handle tables with extra pipes', () => {
      const content = `|| Extra | Pipes | Here ||
||-------|-------|------||
|| A     | B     | C    ||`;
      const result = markdownParse.parse(content);
      expect(result).toBeDefined();
    });

    it('should handle tables with escaped pipes', () => {
      const content = `| Column 1 | Column 2 |
|----------|----------|
| Pipe \\| Here | Normal |
| \\|Start | End\\| |`;
      const result = markdownParse.parse(content);
      expect(result).toContain('<table>');
      expect(result).toContain('Pipe | Here');
      expect(result).toContain('|Start');
      expect(result).toContain('End|');
    });

    it('should handle malformed table syntax', () => {
      const malformedTables = [
        `| Missing
|----
| Pipes`,
        `| Too | Many | Separators |
|-----|-----|-----|-----|
| Few | Cells |`,
        `| No separator row |
| Direct to content |`,
      ];

      malformedTables.forEach(content => {
        const result = markdownParse.parse(content);
        expect(result).toBeDefined();
        // Should handle gracefully without errors
      });
    });

    it('should handle empty tables', () => {
      const content = `|  |  |  |
|--|--|--|
|  |  |  |`;
      const result = markdownParse.parse(content);
      expect(result).toContain('<table>');
      expect(result).toContain('<td></td>');
    });

    it('should handle single row tables', () => {
      const content = `| Header 1 | Header 2 | Header 3 |
|----------|----------|----------|`;
      const result = markdownParse.parse(content);
      expect(result).toContain('<table>');
      expect(result).toContain('<thead>');
      expect(result).toContain('<th>Header 1</th>');
    });
  });

  describe('Tables in Context', () => {
    it('should handle tables with surrounding content', () => {
      const content = `# Table Example

Here's some text before the table.

| Name | Age | City |
|------|-----|------|
| John | 30  | NYC  |
| Jane | 25  | LA   |

And here's some text after the table.

## Another Section`;
      const result = markdownParse.parse(content);
      expect(result).toContain('<h1>Table Example</h1>');
      expect(result).toContain('text before the table');
      expect(result).toContain('<table>');
      expect(result).toContain('<th>Name</th>');
      expect(result).toContain('<td>John</td>');
      expect(result).toContain('text after the table');
      expect(result).toContain('<h2>Another Section</h2>');
    });

    it('should handle multiple tables', () => {
      const content = `## Table 1

| A | B |
|---|---|
| 1 | 2 |

## Table 2

| X | Y | Z |
|---|---|---|
| 1 | 2 | 3 |
| 4 | 5 | 6 |

## Table 3

| Name | Status |
|------|--------|
| Task 1 | ✅ |
| Task 2 | ❌ |`;
      const result = markdownParse.parse(content);
      expect((result.match(/<table>/g) || []).length).toBe(3);
      expect(result).toContain('<th>A</th>');
      expect(result).toContain('<th>X</th>');
      expect(result).toContain('<th>Name</th>');
      expect(result).toContain('✅');
      expect(result).toContain('❌');
    });

    it('should handle tables in lists', () => {
      const content = `1. First item with table:

   | Col1 | Col2 |
   |------|------|
   | A    | B    |

2. Second item

3. Third item with table:

   | Name | Value |
   |------|-------|
   | X    | 100   |
   | Y    | 200   |`;
      const result = markdownParse.parse(content);
      expect(result).toContain('<ol>');
      expect((result.match(/<table>/g) || []).length).toBe(2);
      expect(result).toContain('<th>Col1</th>');
      expect(result).toContain('<th>Name</th>');
      expect(result).toContain('<td>A</td>');
      expect(result).toContain('<td>100</td>');
    });

    it('should handle tables in blockquotes', () => {
      const content = `> Here's a table in a blockquote:
> 
> | Item | Price |
> |------|-------|
> | Book | $10   |
> | Pen  | $2    |
>
> End of quote.`;
      const result = markdownParse.parse(content);
      expect(result).toContain('<blockquote>');
      expect(result).toContain('<table>');
      expect(result).toContain('<th>Item</th>');
      expect(result).toContain('<td>Book</td>');
      expect(result).toContain('$10');
    });
  });

  describe('Performance with Large Tables', () => {
    it('should handle tables with many rows', () => {
      const headers = '| ID | Name | Status | Value |';
      const separator = '|----|------|--------|-------|';
      const rows = Array.from({ length: 100 }, (_, i) => 
        `| ${i + 1} | Item ${i + 1} | Active | ${(i + 1) * 10} |`
      ).join('\n');
      
      const content = `${headers}\n${separator}\n${rows}`;
      const result = markdownParse.parse(content);
      expect(result).toContain('<table>');
      expect(result).toContain('<th>ID</th>');
      expect(result).toContain('<td>1</td>');
      expect(result).toContain('<td>100</td>');
      expect(result).toContain('Item 1');
      expect(result).toContain('Item 100');
      expect((result.match(/<tr>/g) || []).length).toBeGreaterThanOrEqual(100);
    });

    it('should handle tables with complex mixed content', () => {
      const content = `| **Feature** | *Description* | \`Code\` | [Link](url) | ![Img](i.png) |
|-------------|---------------|----------|-------------|---------------|
| **Bold**    | *Emphasis*    | \`inline\` | [Google](g.com) | ![Icon](icon.png) |
| ~~Strike~~  | ==Highlight== | \`code\`   | [GitHub](gh.com) | ![Photo](photo.jpg) |
| Normal      | Plain text    | \`func()\` | [Stack](so.com) | ![Logo](logo.svg) |`;
      const result = markdownParse.parse(content);
      expect(result).toContain('<table>');
      expect(result).toContain('<strong>Feature</strong>');
      expect(result).toContain('<em>Description</em>');
      expect(result).toContain('<code>Code</code>');
      expect(result).toContain('<a href="url">Link</a>');
      expect(result).toContain('src="i.png"');
      expect(result).toContain('data-type="SvgEditor"'); // For SVG
    });
  });
});