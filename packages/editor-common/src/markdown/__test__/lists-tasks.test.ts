import { MarkdownParse } from '../markdown-parse';

// Mock dependencies
jest.mock('@tiptap/core', () => ({
  elementFromString: jest.fn((content: string) => {
    const parser = new DOMParser();
    return parser.parseFromString(`<body>${content}</body>`, 'text/html').body;
  }),
}));

jest.mock('../../extensions', () => ({
  EditorSchema: {
    nodes: {
      paragraph: { isBlock: true, spec: { parseDOM: [{ tag: 'p' }] } },
      heading: { isBlock: true, spec: { parseDOM: [{ tag: 'h1' }, { tag: 'h2' }] } },
      blockquote: { isBlock: true, spec: { parseDOM: [{ tag: 'blockquote' }] } },
    },
  },
}));

describe('MarkdownParse - Lists and Tasks', () => {
  let markdownParse: MarkdownParse;

  beforeEach(() => {
    markdownParse = new MarkdownParse();
  });

  describe('Unordered Lists', () => {
    it('should handle basic unordered lists with different markers', () => {
      const markers = ['-', '*', '+'];
      markers.forEach(marker => {
        const content = `${marker} Item 1
${marker} Item 2
${marker} Item 3`;
        const result = markdownParse.parse(content);
        expect(result).toContain('<ul>');
        expect(result).toContain('<li>Item 1</li>');
        expect(result).toContain('<li>Item 2</li>');
        expect(result).toContain('<li>Item 3</li>');
      });
    });

    it('should handle mixed markers in the same list', () => {
      const content = `- Item with dash
* Item with asterisk
+ Item with plus
- Back to dash`;
      const result = markdownParse.parse(content);
      expect(result).toContain('<ul>');
      expect(result).toContain('Item with dash');
      expect(result).toContain('Item with asterisk');
      expect(result).toContain('Item with plus');
    });

    it('should handle lists with complex content', () => {
      const content = `- Item with **bold** text
- Item with *italic* text
- Item with \`inline code\`
- Item with [link](http://example.com)
- Item with ![image](image.jpg)
- Item with ~~strikethrough~~
- Item with ==highlight==`;
      const result = markdownParse.parse(content);
      expect(result).toContain('<ul>');
      expect(result).toContain('<strong>bold</strong>');
      expect(result).toContain('<em>italic</em>');
      expect(result).toContain('<code>inline code</code>');
      expect(result).toContain('<a href="http://example.com">');
    });

    it('should handle list items with multiple paragraphs', () => {
      const content = `- First item with single paragraph

- Second item with first paragraph

  Second paragraph of second item
  
  Third paragraph of second item

- Third item`;
      const result = markdownParse.parse(content);
      expect(result).toContain('<ul>');
      expect(result).toContain('First item');
      expect(result).toContain('Second item');
      expect(result).toContain('Second paragraph');
      expect(result).toContain('Third item');
    });

    it('should handle very long list items', () => {
      const longText = 'This is a very long list item that contains '.repeat(20);
      const content = `- Short item
- ${longText}more text
- Another short item`;
      const result = markdownParse.parse(content);
      expect(result).toContain('<ul>');
      expect(result).toContain('Short item');
      expect(result).toContain(longText);
      expect(result).toContain('Another short item');
    });
  });

  describe('Ordered Lists', () => {
    it('should handle basic ordered lists', () => {
      const content = `1. First item
2. Second item
3. Third item`;
      const result = markdownParse.parse(content);
      expect(result).toContain('<ol>');
      expect(result).toContain('<li>First item</li>');
      expect(result).toContain('<li>Second item</li>');
      expect(result).toContain('<li>Third item</li>');
    });

    it('should handle ordered lists with different numbering', () => {
      const content = `1. First item
1. Second item (also numbered 1)
1. Third item (also numbered 1)`;
      const result = markdownParse.parse(content);
      expect(result).toContain('<ol>');
      expect(result).toContain('First item');
      expect(result).toContain('Second item');
      expect(result).toContain('Third item');
    });

    it('should handle ordered lists starting from different numbers', () => {
      const content = `5. Fifth item
6. Sixth item
7. Seventh item`;
      const result = markdownParse.parse(content);
      expect(result).toContain('<ol');
      expect(result).toContain('start="5"');
      expect(result).toContain('Fifth item');
    });

    it('should handle ordered lists with non-sequential numbering', () => {
      const content = `1. First
3. Third (but second in order)
100. Hundred (but third in order)
2. Two (but fourth in order)`;
      const result = markdownParse.parse(content);
      expect(result).toContain('<ol>');
      expect(result).toContain('First');
      expect(result).toContain('Third');
      expect(result).toContain('Hundred');
      expect(result).toContain('Two');
    });

    it('should handle ordered lists with complex content', () => {
      const content = `1. **Bold** first item
2. *Italic* second item with [link](http://example.com)
3. Item with \`code\` and ![image](img.jpg)
4. Item with multiple **bold** and *italic* formatting
5. Final item with ~~strikethrough~~ text`;
      const result = markdownParse.parse(content);
      expect(result).toContain('<ol>');
      expect(result).toContain('<strong>Bold</strong>');
      expect(result).toContain('<em>Italic</em>');
      expect(result).toContain('<code>code</code>');
      expect(result).toContain('<s>strikethrough</s>');
    });
  });

  describe('Nested Lists', () => {
    it('should handle deeply nested unordered lists', () => {
      const content = `- Level 1
  - Level 2
    - Level 3
      - Level 4
        - Level 5
          - Level 6`;
      const result = markdownParse.parse(content);
      expect(result).toContain('<ul>');
      expect((result.match(/<ul>/g) || []).length).toBeGreaterThanOrEqual(3);
      expect(result).toContain('Level 1');
      expect(result).toContain('Level 6');
    });

    it('should handle deeply nested ordered lists', () => {
      const content = `1. Level 1
   1. Level 2
      1. Level 3
         1. Level 4
            1. Level 5`;
      const result = markdownParse.parse(content);
      expect(result).toContain('<ol>');
      expect((result.match(/<ol>/g) || []).length).toBeGreaterThanOrEqual(3);
      expect(result).toContain('Level 1');
      expect(result).toContain('Level 5');
    });

    it('should handle mixed nested lists', () => {
      const content = `1. Ordered level 1
   - Unordered level 2
     1. Ordered level 3
        - Unordered level 4
          1. Ordered level 5
2. Back to ordered level 1
   * Different unordered marker level 2`;
      const result = markdownParse.parse(content);
      expect(result).toContain('<ol>');
      expect(result).toContain('<ul>');
      expect(result).toContain('Ordered level 1');
      expect(result).toContain('Unordered level 2');
      expect(result).toContain('Ordered level 5');
    });

    it('should handle complex nested structures with content', () => {
      const content = `1. **First** main item
   - Nested **unordered** with *emphasis*
   - Another nested with \`code\`
     1. Deep **ordered** item
     2. Another deep item with [link](url)
        - Even deeper unordered
          1. Very deep ordered
2. **Second** main item with complex content

   This is a paragraph within the list item.
   
   - Nested list after paragraph
   - Another nested item
   
   Another paragraph.

3. **Third** main item`;
      const result = markdownParse.parse(content);
      expect(result).toContain('<ol>');
      expect(result).toContain('<ul>');
      expect(result).toContain('<strong>First</strong>');
      expect(result).toContain('<strong>Second</strong>');
      expect(result).toContain('paragraph within');
      expect(result).toContain('Very deep ordered');
    });

    it('should handle nested lists with code blocks', () => {
      const content = `1. Item with code block:

   \`\`\`javascript
   function example() {
     return "nested code";
   }
   \`\`\`

2. Item with nested list and code:
   - Nested item
   - Another nested with inline \`code\`
   - Nested with block:
   
     \`\`\`python
     print("nested in nested")
     \`\`\``;
      const result = markdownParse.parse(content);
      expect(result).toContain('<ol>');
      expect(result).toContain('<ul>');
      expect(result).toContain('<pre class="javascript">');
      expect(result).toContain('<pre class="python">');
      expect(result).toContain('function example');
    });

    it('should handle nested lists with blockquotes', () => {
      const content = `1. Item with quote:

   > This is a blockquote within a list item
   > with multiple lines

2. Item with nested list and quotes:
   - Nested item
   - Nested with quote:
   
     > Nested blockquote
     > Second line of quote`;
      const result = markdownParse.parse(content);
      expect(result).toContain('<ol>');
      expect(result).toContain('<ul>');
      expect(result).toContain('<blockquote>');
      expect(result).toContain('blockquote within');
      expect(result).toContain('Nested blockquote');
    });
  });

  describe('Task Lists', () => {
    it('should handle basic task lists', () => {
      const content = `- [ ] Unchecked task 1
- [x] Checked task 1
- [ ] Unchecked task 2
- [X] Checked task 2 (uppercase X)
- [x] Checked task 3`;
      const result = markdownParse.parse(content);
      expect(result).toContain('data-type="taskList"');
      expect(result).toContain('data-type="taskItem"');
      expect(result).toContain('data-checked="false"');
      expect(result).toContain('data-checked="true"');
      expect(result).not.toContain('<input'); // Inputs should be removed
      expect(result).not.toContain('type="checkbox"');
    });

    it('should handle task lists with complex content', () => {
      const content = `- [ ] Task with **bold** text
- [x] Completed task with *italic* text
- [ ] Task with \`inline code\`
- [x] Task with [link](http://example.com)
- [ ] Task with ![image](task-image.jpg)
- [x] Task with ~~completed strikethrough~~
- [ ] Task with ==highlighted== text`;
      const result = markdownParse.parse(content);
      expect(result).toContain('data-type="taskList"');
      expect(result).toContain('<strong>bold</strong>');
      expect(result).toContain('<em>italic</em>');
      expect(result).toContain('<code>inline code</code>');
      expect(result).toContain('<a href="http://example.com">');
      expect(result).toContain('<s>completed strikethrough</s>');
    });

    it('should handle nested task lists', () => {
      const content = `- [ ] Main task 1
  - [ ] Subtask 1.1
  - [x] Subtask 1.2 (completed)
    - [ ] Sub-subtask 1.2.1
    - [x] Sub-subtask 1.2.2 (completed)
- [x] Main task 2 (completed)
  - [x] Subtask 2.1 (completed)
  - [ ] Subtask 2.2
- [ ] Main task 3`;
      const result = markdownParse.parse(content);
      expect(result).toContain('data-type="taskList"');
      expect(result).toContain('data-type="taskItem"');
      expect(result).toContain('Main task 1');
      expect(result).toContain('Subtask 1.1');
      expect(result).toContain('Sub-subtask 1.2.1');
      // Check for multiple levels of nesting
      expect((result.match(/data-type="taskList"/g) || []).length).toBeGreaterThanOrEqual(2);
    });

    it('should handle mixed task and regular lists', () => {
      const content = `1. Regular ordered item
   - [ ] Task within ordered list
   - [x] Completed task within ordered list
2. Another ordered item
   - Regular unordered item
   - [ ] Another task
     - Regular nested item
     - [x] Nested completed task`;
      const result = markdownParse.parse(content);
      expect(result).toContain('<ol>');
      expect(result).toContain('<ul>');
      expect(result).toContain('data-type="taskList"');
      expect(result).toContain('data-type="taskItem"');
      expect(result).toContain('Regular ordered item');
      expect(result).toContain('Regular unordered item');
    });

    it('should handle task lists with multiline content', () => {
      const content = `- [ ] Task with multiple lines
  
  This is additional content for the first task.
  It spans multiple paragraphs.
  
  Even more content here.

- [x] Completed task with content
  
  This task has been completed and has
  additional descriptive content.

- [ ] Simple task without extra content`;
      const result = markdownParse.parse(content);
      expect(result).toContain('data-type="taskList"');
      expect(result).toContain('Task with multiple lines');
      expect(result).toContain('additional content');
      expect(result).toContain('Completed task');
    });

    it('should handle task lists with code blocks and quotes', () => {
      const content = `- [ ] Task with code:

  \`\`\`javascript
  function completeTask() {
    return true;
  }
  \`\`\`

- [x] Task with quote:

  > This task has been completed
  > and includes a helpful quote

- [ ] Task with inline \`code\` and formatting`;
      const result = markdownParse.parse(content);
      expect(result).toContain('data-type="taskList"');
      expect(result).toContain('<pre class="javascript">');
      expect(result).toContain('<blockquote>');
      expect(result).toContain('function completeTask');
      expect(result).toContain('<code>code</code>');
    });

    it('should handle malformed task syntax gracefully', () => {
      const content = `- [] Empty brackets
- [y] Invalid character
- [  ] Spaces in brackets
- [ x] Space before x
- [x ] Space after x
- [xx] Double x
- [ ] Valid unchecked
- [x] Valid checked`;
      const result = markdownParse.parse(content);
      expect(result).toBeDefined();
      // Should still contain valid task items
      expect(result).toContain('data-type="taskItem"');
      expect(result).toContain('data-checked="false"');
      expect(result).toContain('data-checked="true"');
    });
  });

  describe('Complex List Scenarios', () => {
    it('should handle lists interrupted by other elements', () => {
      const content = `1. First ordered item
2. Second ordered item

# Heading interrupts the list

3. This starts a new list
4. Fourth item in new list

- Unordered list
- Second unordered item

> Blockquote interrupts

- New unordered list
- Second item in new unordered list`;
      const result = markdownParse.parse(content);
      expect(result).toContain('<ol>');
      expect(result).toContain('<ul>');
      expect(result).toContain('<h1>Heading interrupts</h1>');
      expect(result).toContain('<blockquote>');
      // Should have multiple separate lists
      expect((result.match(/<ol>/g) || []).length).toBe(2);
      expect((result.match(/<ul>/g) || []).length).toBe(2);
    });

    it('should handle very long lists', () => {
      const items = Array.from({ length: 100 }, (_, i) => `- Item ${i + 1}`);
      const content = items.join('\n');
      const result = markdownParse.parse(content);
      expect(result).toContain('<ul>');
      expect(result).toContain('Item 1');
      expect(result).toContain('Item 100');
      expect((result.match(/<li>/g) || []).length).toBe(100);
    });

    it('should handle lists with tables', () => {
      const content = `1. Item with table:

   | Column 1 | Column 2 |
   |----------|----------|
   | Data 1   | Data 2   |

2. Regular item

3. Another item with table:

   | Name | Status |
   |------|--------|
   | Task 1 | ✅ Done |
   | Task 2 | ⏳ Pending |`;
      const result = markdownParse.parse(content);
      expect(result).toContain('<ol>');
      expect(result).toContain('<table>');
      expect(result).toContain('<th>Column 1</th>');
      expect(result).toContain('<td>Data 1</td>');
      expect(result).toContain('✅ Done');
    });

    it('should handle lists with horizontal rules', () => {
      const content = `- Item before rule

---

- Item after rule
- Another item

***

- Item after asterisk rule`;
      const result = markdownParse.parse(content);
      expect(result).toContain('<ul>');
      expect(result).toContain('<hr>');
      expect(result).toContain('Item before rule');
      expect(result).toContain('Item after rule');
      expect(result).toContain('Item after asterisk rule');
    });

    it('should handle performance with complex nested structures', () => {
      let content = '';
      for (let i = 0; i < 5; i++) {
        for (let j = 0; j < 5; j++) {
          const indent = '  '.repeat(j);
          content += `${indent}- Level ${j + 1}, Item ${i + 1}\n`;
          if (j < 2) {
            content += `${indent}  - [ ] Task at level ${j + 2}\n`;
            content += `${indent}  - [x] Completed task at level ${j + 2}\n`;
          }
        }
      }
      
      const result = markdownParse.parse(content);
      expect(result).toContain('<ul>');
      expect(result).toContain('data-type="taskList"');
      expect(result).toContain('Level 1, Item 1');
      expect(result).toContain('Level 5, Item 5');
      expect(result.length).toBeGreaterThan(1000); // Should be substantial content
    });
  });
});