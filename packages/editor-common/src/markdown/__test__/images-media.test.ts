import { MarkdownParse } from '../markdown-parse';

// Mock dependencies
jest.mock('@tiptap/core', () => ({
  elementFromString: jest.fn((content: string) => {
    const parser = new DOMParser();
    return parser.parseFromString(`<body>${content}</body>`, 'text/html').body;
  }),
}));

jest.mock('../../extensions', () => ({
  EditorSchema: {
    nodes: {
      paragraph: { isBlock: true, spec: { parseDOM: [{ tag: 'p' }] } },
      heading: { isBlock: true, spec: { parseDOM: [{ tag: 'h1' }, { tag: 'h2' }] } },
      blockquote: { isBlock: true, spec: { parseDOM: [{ tag: 'blockquote' }] } },
    },
  },
}));

jest.mock('../../const/image', () => ({
  ImageStatus: {
    LOADING: 'loading',
    LOADED: 'loaded',
    FAILED: 'failed',
    UPLOAD_IMAGE: 'uploadImage',
  },
  generateImageUniqueId: jest.fn(() => 'imageId-test-123'),
}));

describe('MarkdownParse - Images and Media', () => {
  let markdownParse: MarkdownParse;

  beforeEach(() => {
    markdownParse = new MarkdownParse();
  });

  describe('Basic Image Parsing', () => {
    it('should handle simple images', () => {
      const content = '![Alt text](image.jpg)';
      const result = markdownParse.parse(content);
      expect(result).toContain('<img');
      expect(result).toContain('src="image.jpg"');
      expect(result).toContain('alt="Alt text"');
      expect(result).toContain('id="imageId-test-123"');
      expect(result).toContain('status="loading"');
    });

    it('should handle images with various file extensions', () => {
      const extensions = ['jpg', 'jpeg', 'png', 'gif', 'webp', 'bmp', 'tiff', 'ico'];
      extensions.forEach(ext => {
        const content = `![${ext.toUpperCase()} image](image.${ext})`;
        const result = markdownParse.parse(content);
        expect(result).toContain(`src="image.${ext}"`);
        expect(result).toContain(`alt="${ext.toUpperCase()} image"`);
      });
    });

    it('should handle images with no alt text', () => {
      const content = '![](image.jpg)';
      const result = markdownParse.parse(content);
      expect(result).toContain('<img');
      expect(result).toContain('src="image.jpg"');
      expect(result).toContain('alt=""');
    });

    it('should handle images with complex alt text', () => {
      const content = '![This is a **complex** alt text with *formatting* and `code`](image.jpg)';
      const result = markdownParse.parse(content);
      expect(result).toContain('<img');
      expect(result).toContain('src="image.jpg"');
      expect(result).toContain('This is a **complex** alt text');
    });

    it('should handle images with URLs as sources', () => {
      const urls = [
        'https://example.com/image.jpg',
        'http://cdn.example.com/path/to/image.png',
        'https://images.unsplash.com/photo-123456',
        'https://via.placeholder.com/300x200',
      ];

      urls.forEach(url => {
        const content = `![Online image](${url})`;
        const result = markdownParse.parse(content);
        expect(result).toContain(`src="${url}"`);
        expect(result).toContain('alt="Online image"');
      });
    });
  });

  describe('Special Media Types', () => {
    it('should handle SVG files', () => {
      const content = '![Diagram](diagram.svg)';
      const result = markdownParse.parse(content);
      expect(result).toContain('data-type="SvgEditor"');
      expect(result).toContain('data-src="diagram.svg"');
      expect(result).toContain('data-alt="Diagram"');
      expect(result).not.toContain('<img');
    });

    it('should handle various SVG scenarios', () => {
      const svgTests = [
        '![Simple SVG](icon.svg)',
        '![Complex diagram](flowchart-diagram.svg)',
        '![Logo](company-logo.SVG)', // Test case sensitivity
        '![Vector art](illustration.svg)',
      ];

      svgTests.forEach(content => {
        const result = markdownParse.parse(content);
        expect(result).toContain('data-type="SvgEditor"');
        expect(result).toContain('.svg"') || expect(result).toContain('.SVG"');
      });
    });

    it('should handle audio files', () => {
      const content = '![Audio file](audio.mp3)';
      const result = markdownParse.parse(content);
      expect(result).toContain('data-type="Audio"');
      expect(result).toContain('data-src="audio.mp3"');
      expect(result).toContain('data-alt="Audio file"');
      expect(result).not.toContain('<img');
    });

    it('should handle various audio formats', () => {
      const audioFormats = ['mp3', 'wav', 'ogg', 'flac', 'm4a'];
      audioFormats.forEach(format => {
        const content = `![${format.toUpperCase()} audio](sound.${format})`;
        const result = markdownParse.parse(content);
        if (format === 'mp3') {
          expect(result).toContain('data-type="Audio"');
        } else {
          // Only mp3 is specifically handled as audio
          expect(result).toContain('<img');
        }
      });
    });

    it('should handle video files referenced as images', () => {
      const videoFormats = ['mp4', 'avi', 'mov', 'webm', 'mkv'];
      videoFormats.forEach(format => {
        const content = `![${format.toUpperCase()} video](video.${format})`;
        const result = markdownParse.parse(content);
        // Videos are treated as regular images currently
        expect(result).toContain('<img');
        expect(result).toContain(`src="video.${format}"`);
      });
    });
  });

  describe('Image Attributes and JSON Alt Text', () => {
    it('should parse JSON attributes in alt text', () => {
      const jsonAlt = JSON.stringify({
        alt: 'Custom Alt Text',
        status: 'loaded',
        width: 800,
        height: 600,
        id: 'custom-image-id',
        text: 'Image caption',
      });
      const content = `![${jsonAlt}](image.jpg)`;
      const result = markdownParse.parse(content);
      expect(result).toContain('alt="Custom Alt Text"');
      expect(result).toContain('status="loaded"');
      expect(result).toContain('width="800"');
      expect(result).toContain('height="600"');
      expect(result).toContain('id="custom-image-id"');
      expect(result).toContain('text="Image caption"');
    });

    it('should handle various JSON attribute combinations', () => {
      const testCases = [
        { alt: 'Simple alt', status: 'loading' },
        { width: 400, height: 300, status: 'loaded' },
        { id: 'my-image', alt: 'My Image', text: 'Caption' },
        { status: 'failed', alt: 'Failed to load' },
        { status: 'uploadImage', alt: 'Uploading...' },
      ];

      testCases.forEach((attrs, index) => {
        const jsonAlt = JSON.stringify(attrs);
        const content = `![${jsonAlt}](image-${index}.jpg)`;
        const result = markdownParse.parse(content);
        
        Object.entries(attrs).forEach(([key, value]) => {
          expect(result).toContain(`${key}="${value}"`);
        });
      });
    });

    it('should fallback to default attributes for malformed JSON', () => {
      const malformedCases = [
        '![{invalid json](image.jpg)',
        '![{"unclosed": "json"](image.jpg)',
        '![{mixed: "quotes\'}](image.jpg)',
        '![not json at all](image.jpg)',
      ];

      malformedCases.forEach(content => {
        const result = markdownParse.parse(content);
        expect(result).toContain('<img');
        expect(result).toContain('status="loading"');
        expect(result).toContain('id="imageId-test-123"');
      });
    });

    it('should handle partial JSON attributes', () => {
      const jsonAlt = JSON.stringify({
        alt: 'Partial attributes',
        width: 500,
        // Missing height, status, id, text
      });
      const content = `![${jsonAlt}](image.jpg)`;
      const result = markdownParse.parse(content);
      expect(result).toContain('alt="Partial attributes"');
      expect(result).toContain('width="500"');
      expect(result).toContain('height="undefined"'); // Should be present even if undefined
      expect(result).toContain('id="imageId-test-123"'); // Generated ID
    });
  });

  describe('Linked Images', () => {
    it('should handle images wrapped in links', () => {
      const content = '[![Image](thumbnail.jpg)](full-size.jpg)';
      const result = markdownParse.parse(content);
      expect(result).toContain('<a href="full-size.jpg">');
      expect(result).toContain('<img');
      expect(result).toContain('src="thumbnail.jpg"');
      expect(result).toContain('alt="Image"');
    });

    it('should handle complex linked image scenarios', () => {
      const content = `[![Complex Image with **bold** alt](thumb.jpg "Thumbnail")](https://example.com/gallery/full.jpg "Full size image")`;
      const result = markdownParse.parse(content);
      expect(result).toContain('<a href="https://example.com/gallery/full.jpg"');
      expect(result).toContain('title="Full size image"');
      expect(result).toContain('<img');
      expect(result).toContain('Complex Image with **bold** alt');
    });

    it('should handle images linked to external URLs', () => {
      const content = '[![External](local.jpg)](https://external-site.com)';
      const result = markdownParse.parse(content);
      expect(result).toContain('<a href="https://external-site.com">');
      expect(result).toContain('src="local.jpg"');
    });
  });

  describe('Image Positioning and Context', () => {
    it('should handle images in various contexts', () => {
      const content = `# Gallery

Here's an inline image: ![Inline](inline.jpg) within text.

## Standalone Images

![Standalone](standalone.jpg)

### In Lists

- ![List item 1](item1.jpg)
- ![List item 2](item2.jpg)

### In Blockquotes

> ![Quote image](quote.jpg)
> This image is inside a blockquote.

### In Tables

| Image | Description |
|-------|-------------|
| ![Table image](table.jpg) | Image in table |`;

      const result = markdownParse.parse(content);
      expect(result).toContain('<h1>Gallery</h1>');
      expect(result).toContain('src="inline.jpg"');
      expect(result).toContain('src="standalone.jpg"');
      expect(result).toContain('src="item1.jpg"');
      expect(result).toContain('src="quote.jpg"');
      expect(result).toContain('src="table.jpg"');
    });

    it('should handle multiple images in sequence', () => {
      const content = `![Image 1](img1.jpg)
![Image 2](img2.jpg)
![Image 3](img3.jpg)

Text between

![Image 4](img4.jpg) ![Image 5](img5.jpg)`;
      const result = markdownParse.parse(content);
      expect(result).toContain('src="img1.jpg"');
      expect(result).toContain('src="img2.jpg"');
      expect(result).toContain('src="img3.jpg"');
      expect(result).toContain('src="img4.jpg"');
      expect(result).toContain('src="img5.jpg"');
    });

    it('should handle images with surrounding formatting', () => {
      const content = `**Bold text with ![image](bold.jpg) inside.**
*Italic text with ![image](italic.jpg) inside.*
~~Strike text with ![image](strike.jpg) inside.~~
\`Code with ![image](code.jpg) inside.\`
[Link with ![image](link.jpg) inside](http://example.com)`;
      const result = markdownParse.parse(content);
      expect(result).toContain('<strong>');
      expect(result).toContain('src="bold.jpg"');
      expect(result).toContain('<em>');
      expect(result).toContain('src="italic.jpg"');
    });
  });

  describe('Error Handling and Edge Cases', () => {
    it('should handle missing image sources', () => {
      const content = '![Alt text]()';
      const result = markdownParse.parse(content);
      expect(result).toContain('<img');
      expect(result).toContain('src=""');
      expect(result).toContain('alt="Alt text"');
    });

    it('should handle images with special characters in URLs', () => {
      const specialUrls = [
        'image with spaces.jpg',
        'image-with-dashes.jpg',
        'image_with_underscores.jpg',
        'image%20with%20encoding.jpg',
        '<EMAIL>',
        'image(1).jpg',
        'image[variant].jpg',
      ];

      specialUrls.forEach(url => {
        const content = `![Special](${url})`;
        const result = markdownParse.parse(content);
        expect(result).toContain(`src="${url}"`);
      });
    });

    it('should handle very long image URLs and alt text', () => {
      const longUrl = 'https://example.com/' + 'very-long-path/'.repeat(20) + 'image.jpg';
      const longAlt = 'This is a very long alt text '.repeat(10);
      const content = `![${longAlt}](${longUrl})`;
      const result = markdownParse.parse(content);
      expect(result).toContain(`src="${longUrl}"`);
      expect(result).toContain(longAlt);
    });

    it('should handle malformed image syntax', () => {
      const malformedCases = [
        '![Alt text](image.jpg', // Missing closing parenthesis
        '![Alt text(image.jpg)', // Missing closing bracket
        '!Alt text](image.jpg)', // Missing opening bracket
        '[Alt text](image.jpg)', // Missing exclamation mark
      ];

      malformedCases.forEach(content => {
        const result = markdownParse.parse(content);
        expect(result).toBeDefined();
        // Should handle gracefully without throwing errors
      });
    });

    it('should handle images with data URLs', () => {
      const dataUrl = 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNk+M9QDwADhgGAWjR9awAAAABJRU5ErkJggg==';
      const content = `![Data image](${dataUrl})`;
      const result = markdownParse.parse(content);
      expect(result).toContain('<img');
      expect(result).toContain('data:image/png;base64');
    });

    it('should handle images with query parameters', () => {
      const urlWithParams = 'https://example.com/image.jpg?w=800&h=600&q=85&format=webp';
      const content = `![Parameterized image](${urlWithParams})`;
      const result = markdownParse.parse(content);
      expect(result).toContain(`src="${urlWithParams}"`);
    });
  });

  describe('Performance with Many Images', () => {
    it('should handle many images efficiently', () => {
      const images = Array.from({ length: 50 }, (_, i) => 
        `![Image ${i + 1}](image-${i + 1}.jpg)`
      ).join('\n\n');
      
      const result = markdownParse.parse(images);
      expect(result).toContain('src="image-1.jpg"');
      expect(result).toContain('src="image-50.jpg"');
      expect((result.match(/<img/g) || []).length).toBe(50);
    });

    it('should handle mixed media types efficiently', () => {
      const mediaTypes = [
        '![Regular image](image.jpg)',
        '![SVG diagram](diagram.svg)',
        '![Audio file](sound.mp3)',
        '![Another image](photo.png)',
        '![Vector graphic](logo.svg)',
        '![Music track](music.mp3)',
      ];

      const content = mediaTypes.join('\n\n');
      const result = markdownParse.parse(content);
      expect(result).toContain('<img');
      expect(result).toContain('data-type="SvgEditor"');
      expect(result).toContain('data-type="Audio"');
    });
  });
});