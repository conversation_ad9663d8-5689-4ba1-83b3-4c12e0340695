import { MarkdownParse } from '../markdown-parse';

// Mock dependencies
jest.mock('@tiptap/core', () => ({
  elementFromString: jest.fn((content: string) => {
    const parser = new DOMParser();
    return parser.parseFromString(`<body>${content}</body>`, 'text/html').body;
  }),
}));

jest.mock('../../extensions', () => ({
  EditorSchema: {
    nodes: {
      paragraph: { isBlock: true, spec: { parseDOM: [{ tag: 'p' }] } },
      heading: { isBlock: true, spec: { parseDOM: [{ tag: 'h1' }, { tag: 'h2' }] } },
      blockquote: { isBlock: true, spec: { parseDOM: [{ tag: 'blockquote' }] } },
    },
  },
}));

describe('MarkdownParse - Code Blocks', () => {
  let markdownParse: MarkdownParse;

  beforeEach(() => {
    markdownParse = new MarkdownParse();
  });

  describe('Basic Code Blocks', () => {
    it('should handle indented code blocks', () => {
      const content = `Regular paragraph.

    This is an indented code block
    with multiple lines
    preserving whitespace`;
      const result = markdownParse.parse(content);
      expect(result).toContain('<pre>');
      expect(result).toContain('<code>');
      expect(result).toContain('indented code block');
    });

    it('should handle fenced code blocks with various languages', () => {
      const languages = [
        'javascript', 'typescript', 'python', 'java', 'cpp', 'c', 'csharp',
        'go', 'rust', 'php', 'ruby', 'swift', 'kotlin', 'scala', 'html',
        'css', 'scss', 'less', 'json', 'yaml', 'xml', 'markdown', 'bash',
        'shell', 'sql', 'dockerfile', 'nginx', 'apache'
      ];

      languages.forEach(lang => {
        const content = `\`\`\`${lang}
// Sample ${lang} code
const example = "test";
\`\`\``;
        const result = markdownParse.parse(content);
        expect(result).toContain(`<pre class="${lang}">`);
        expect(result).toContain(`<code class="${lang}">`);
        expect(result).toContain('Sample');
      });
    });

    it('should handle code blocks with language info and additional parameters', () => {
      const content = `\`\`\`javascript {1,3-5} showLineNumbers
function example() {
  console.log("Line 1");
  console.log("Line 2");
  console.log("Line 3");
  console.log("Line 4");
  console.log("Line 5");
}
\`\`\``;
      const result = markdownParse.parse(content);
      expect(result).toContain('<pre class="javascript">');
      expect(result).toContain('<code class="javascript">');
      expect(result).toContain('function example');
    });

    it('should preserve whitespace and indentation in code blocks', () => {
      const content = `\`\`\`python
def complex_function():
    if condition:
        for i in range(10):
            if nested_condition:
                deeply_nested_call()
            else:
                another_call()
    return result
\`\`\``;
      const result = markdownParse.parse(content);
      expect(result).toContain('    if condition:');
      expect(result).toContain('        for i in range');
      expect(result).toContain('            if nested_condition:');
      expect(result).toContain('                deeply_nested_call()');
    });
  });

  describe('Special Code Block Types', () => {
    it('should handle mermaid diagrams', () => {
      const content = `\`\`\`mermaid
graph TD;
    A[Start] --> B{Decision};
    B -->|Yes| C[Process 1];
    B -->|No| D[Process 2];
    C --> E[End];
    D --> E;
\`\`\``;
      const result = markdownParse.parse(content);
      expect(result).toContain('data-type="mermaid"');
      expect(result).toContain('data-mermaid-container=""');
      expect(result).toContain('data-mermaid-content=""');
      expect(result).toContain('graph TD;');
      expect(result).toContain('A[Start] --> B{Decision}');
    });

    it('should handle various mermaid diagram types', () => {
      const diagrams = [
        {
          type: 'flowchart',
          content: `graph LR;
    A --> B --> C;`
        },
        {
          type: 'sequence',
          content: `sequenceDiagram
    Alice->>Bob: Hello Bob
    Bob-->>Alice: Hello Alice`
        },
        {
          type: 'gantt',
          content: `gantt
    title A Gantt Diagram
    section Section
    Task 1 :a1, 2014-01-01, 30d`
        },
        {
          type: 'pie',
          content: `pie title Pets
    "Dogs" : 386
    "Cats" : 85`
        }
      ];

      diagrams.forEach(({ content: diagramContent }) => {
        const content = `\`\`\`mermaid
${diagramContent}
\`\`\``;
        const result = markdownParse.parse(content);
        expect(result).toContain('data-type="mermaid"');
        expect(result).toContain(diagramContent.split('\n')[0]);
      });
    });

    it('should handle PlantUML-style diagrams in code blocks', () => {
      const content = `\`\`\`plantuml
@startuml
Alice -> Bob: test
Bob -> Alice: ok
@enduml
\`\`\``;
      const result = markdownParse.parse(content);
      expect(result).toContain('<pre class="plantuml">');
      expect(result).toContain('@startuml');
      expect(result).toContain('Alice -> Bob');
    });

    it('should handle math expressions in code blocks', () => {
      const content = `\`\`\`latex
\\begin{equation}
E = mc^2
\\end{equation}

\\sum_{i=1}^{n} i = \\frac{n(n+1)}{2}
\`\`\``;
      const result = markdownParse.parse(content);
      expect(result).toContain('<pre class="latex">');
      expect(result).toContain('E = mc^2');
      expect(result).toContain('\\sum_{i=1}^{n}');
    });
  });

  describe('Code Block Content Edge Cases', () => {
    it('should handle code blocks with backticks inside', () => {
      const content = "```javascript\n// Code with backticks\nconst template = `Hello ${name}`;\nconst nested = ``nested template``;\nconsole.log('Use \\` to escape backticks');\n```";
      const result = markdownParse.parse(content);
      expect(result).toContain('<pre class="javascript">');
      expect(result).toContain('`Hello ${name}`');
      expect(result).toContain('``nested template``');
    });

    it('should handle code blocks with HTML entities', () => {
      const content = `\`\`\`html
<div class="example">
  <p>Content with &lt; and &gt; and &amp;</p>
  <script>
    if (a < b && b > c) {
      console.log("Logic & operators");
    }
  </script>
</div>
\`\`\``;
      const result = markdownParse.parse(content);
      expect(result).toContain('<pre class="html">');
      expect(result).toContain('<div class="example">');
      expect(result).toContain('a < b && b > c');
    });

    it('should handle code blocks with special characters', () => {
      const content = `\`\`\`bash
#!/bin/bash
echo "Special chars: !@#$%^&*()_+-=[]{}|;':\",./<>?"
grep -E '^[A-Z]+$' file.txt
sed 's/old/new/g' input.txt > output.txt
awk '{print $1, $NF}' data.csv
\`\`\``;
      const result = markdownParse.parse(content);
      expect(result).toContain('<pre class="bash">');
      expect(result).toContain('#!/bin/bash');
      expect(result).toContain('!@#$%^&*()_+-=[]{}');
    });

    it('should handle code blocks with unicode and emoji', () => {
      const content = `\`\`\`python
# Unicode and emoji test
message = "Hello 世界 🌍"
emoji_dict = {
    "happy": "😊",
    "sad": "😢",
    "rocket": "🚀"
}
print(f"Testing: {emoji_dict['rocket']}")
\`\`\``;
      const result = markdownParse.parse(content);
      expect(result).toContain('<pre class="python">');
      expect(result).toContain('Hello 世界 🌍');
      expect(result).toContain('😊');
      expect(result).toContain('🚀');
    });

    it('should handle very long code blocks', () => {
      const longCode = Array.from({ length: 100 }, (_, i) => 
        `    console.log("This is line number ${i + 1} of a very long code block");`
      ).join('\n');
      
      const content = `\`\`\`javascript
function longFunction() {
${longCode}
}
\`\`\``;
      const result = markdownParse.parse(content);
      expect(result).toContain('<pre class="javascript">');
      expect(result).toContain('line number 1');
      expect(result).toContain('line number 100');
    });

    it('should handle empty code blocks', () => {
      const content = `Empty code block:
\`\`\`javascript
\`\`\`

Another empty one:
\`\`\`
\`\`\``;
      const result = markdownParse.parse(content);
      expect(result).toContain('<pre class="javascript">');
      expect(result).toContain('<pre><code></code></pre>');
    });

    it('should handle code blocks with only whitespace', () => {
      const content = `\`\`\`text
   
  
     
\`\`\``;
      const result = markdownParse.parse(content);
      expect(result).toContain('<pre class="text">');
      expect(result).toContain('<code class="text">');
    });
  });

  describe('Inline Code', () => {
    it('should handle various inline code scenarios', () => {
      const content = "Regular text with `inline code` here.\nMultiple `code1` and `code2` in one line.\nCode with special chars: `<>&\"'`.\nCode with backticks: `Use \\` to escape`.\nEmpty code: ``.\nCode at start `code` and end `code`.";
      const result = markdownParse.parse(content);
      expect(result).toContain('<code>inline code</code>');
      expect(result).toContain('<code>code1</code>');
      expect(result).toContain('<code>code2</code>');
      expect(result).toContain('<code>&lt;&gt;&amp;&quot;\'</code>');
    });

    it('should handle inline code with multiple backticks', () => {
      const content = `Use \`\`code with \`backtick\`\`\` for nested backticks.
Triple backticks: \`\`\`inline\`\`\`.
Mixed: \`single\` and \`\`double\`\`.`;
      const result = markdownParse.parse(content);
      expect(result).toContain('<code>');
      expect(result).toContain('backtick');
    });

    it('should handle inline code in different contexts', () => {
      const content = `# Heading with \`code\`
## Another heading with \`more code\`

- List item with \`inline code\`
- Another item: \`function()\`

> Blockquote with \`code example\`

**Bold with \`code\` inside**
*Italic with \`code\` inside*
~~Strike with \`code\` inside~~

[Link with \`code\` text](http://example.com)`;
      const result = markdownParse.parse(content);
      expect(result).toContain('<h1>Heading with <code>code</code></h1>');
      expect(result).toContain('<code>more code</code>');
      expect(result).toContain('<code>inline code</code>');
      expect(result).toContain('<code>function()</code>');
      expect(result).toContain('<code>code example</code>');
    });
  });

  describe('Code Block Combinations', () => {
    it('should handle multiple code blocks in sequence', () => {
      const content = `First block:
\`\`\`javascript
console.log("first");
\`\`\`

Second block:
\`\`\`python
print("second")
\`\`\`

Third block:
\`\`\`mermaid
graph TD;
A-->B;
\`\`\`

Fourth block:
\`\`\`
plain text
\`\`\``;
      const result = markdownParse.parse(content);
      expect(result).toContain('<pre class="javascript">');
      expect(result).toContain('<pre class="python">');
      expect(result).toContain('data-type="mermaid"');
      expect(result).toContain('<pre><code>plain text</code></pre>');
    });

    it('should handle code blocks mixed with other elements', () => {
      const content = `# Code Examples

Here's a JavaScript example:

\`\`\`javascript
function hello(name) {
  return \`Hello, \${name}!\`;
}
\`\`\`

And here's how to use it:

1. First, call the function: \`hello("World")\`
2. Then log the result: \`console.log(result)\`

> **Note**: This function uses template literals.

For more complex examples, see the mermaid diagram:

\`\`\`mermaid
graph LR;
    Input --> Function;
    Function --> Output;
\`\`\``;
      const result = markdownParse.parse(content);
      expect(result).toContain('<h1>Code Examples</h1>');
      expect(result).toContain('<pre class="javascript">');
      expect(result).toContain('<ol>');
      expect(result).toContain('<code>hello("World")</code>');
      expect(result).toContain('<blockquote>');
      expect(result).toContain('data-type="mermaid"');
    });
  });
});