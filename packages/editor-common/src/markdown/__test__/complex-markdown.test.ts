import { MarkdownParse } from '../markdown-parse';
import { EMPTY_PARAGRAPH_NODE_MARKDOWN_VAL } from '../const';

// Mock dependencies
jest.mock('@tiptap/core', () => ({
  elementFromString: jest.fn((content: string) => {
    const parser = new DOMParser();
    return parser.parseFromString(`<body>${content}</body>`, 'text/html').body;
  }),
}));

jest.mock('../../extensions', () => ({
  EditorSchema: {
    nodes: {
      paragraph: { isBlock: true, spec: { parseDOM: [{ tag: 'p' }] } },
      heading: { isBlock: true, spec: { parseDOM: [{ tag: 'h1' }, { tag: 'h2' }] } },
      blockquote: { isBlock: true, spec: { parseDOM: [{ tag: 'blockquote' }] } },
    },
  },
}));

jest.mock('../../const/image', () => ({
  ImageStatus: {
    LOADING: 'loading',
    LOADED: 'loaded',
    FAILED: 'failed',
  },
  generateImageUniqueId: jest.fn(() => 'imageId-test-123'),
}));

describe('MarkdownParse - Complex Scenarios', () => {
  let markdownParse: MarkdownParse;

  beforeEach(() => {
    markdownParse = new MarkdownParse();
  });

  describe('Nested Structures', () => {
    it('should handle nested lists with different markers', () => {
      const content = `- Unordered item
  1. Ordered nested
  2. Another ordered
    - More nested unordered
    - Another unordered
      1. Deep ordered
      2. Deep ordered 2`;
      const result = markdownParse.parse(content);
      expect(result).toContain('<ul>');
      expect(result).toContain('<ol>');
      expect(result).toContain('Unordered item');
      expect(result).toContain('Deep ordered');
    });

    it('should handle blockquotes with nested lists', () => {
      const content = `> This is a quote
> 
> With a list:
> - Item 1
> - Item 2
>   - Nested item
> 
> End of quote`;
      const result = markdownParse.parse(content);
      expect(result).toContain('<blockquote>');
      expect(result).toContain('<ul>');
      expect(result).toContain('This is a quote');
      expect(result).toContain('Nested item');
    });

    it('should handle tables with complex content', () => {
      const content = `| **Bold** | *Italic* | \`Code\` |
|----------|----------|---------|
| [Link](http://example.com) | ![Image](test.jpg) | ~~Strike~~ |
| ==Highlight== | Text with<br>line break | Multiple **bold** *italic* |`;
      const result = markdownParse.parse(content);
      expect(result).toContain('<table>');
      expect(result).toContain('<strong>Bold</strong>');
      expect(result).toContain('<em>Italic</em>');
      expect(result).toContain('<code>Code</code>');
      expect(result).toContain('<a href="http://example.com">');
    });

    it('should handle nested blockquotes with different levels', () => {
      const content = `> Level 1
> 
> > Level 2
> > 
> > > Level 3
> > > Deep quote
> > 
> > Back to level 2
> 
> Back to level 1`;
      const result = markdownParse.parse(content);
      expect(result).toContain('<blockquote>');
      const blockquoteCount = (result.match(/<blockquote>/g) || []).length;
      expect(blockquoteCount).toBeGreaterThanOrEqual(3);
    });

    it('should handle code blocks within lists', () => {
      const content = `1. First item with code:
   \`\`\`javascript
   const x = 1;
   console.log(x);
   \`\`\`

2. Second item with inline \`code\`

3. Third item with mermaid:
   \`\`\`mermaid
   graph TD;
   A-->B;
   \`\`\``;
      const result = markdownParse.parse(content);
      expect(result).toContain('<ol>');
      expect(result).toContain('<pre class="javascript">');
      expect(result).toContain('data-type="mermaid"');
      expect(result).toContain('<code>code</code>');
    });

    it('should handle task lists with nested content', () => {
      const content = `- [ ] Uncompleted task with **bold** text
  - [x] Nested completed subtask
  - [ ] Nested incomplete with \`code\`
    - [x] Deep nested completed
- [x] Top level completed with [link](http://example.com)
- [ ] Task with multiple lines
  
  Additional paragraph content`;
      const result = markdownParse.parse(content);
      expect(result).toContain('data-type="taskList"');
      expect(result).toContain('data-checked="true"');
      expect(result).toContain('data-checked="false"');
      expect(result).toContain('<strong>bold</strong>');
      expect(result).toContain('<code>code</code>');
    });
  });

  describe('Mixed Content Types', () => {
    it('should handle headings with inline formatting', () => {
      const content = `# Main **Bold** Title with *Italic*
## Section with \`Code\` and ==Highlight==
### Subsection with [Link](http://example.com)
#### Small heading with ~~Strikethrough~~
##### Smaller with ++Underline++
###### Smallest with multiple **bold** and *italic* text`;
      const result = markdownParse.parse(content);
      expect(result).toContain('<h1>');
      expect(result).toContain('<h2>');
      expect(result).toContain('<h3>');
      expect(result).toContain('<strong>Bold</strong>');
      expect(result).toContain('<em>Italic</em>');
      expect(result).toContain('<code>Code</code>');
    });

    it('should handle complex inline formatting combinations', () => {
      const content = `This is ***bold and italic*** text.
This is **bold with *italic* inside**.
This is *italic with **bold** inside*.
This is \`code with **bold** inside\`.
This is ~~strikethrough with *italic* inside~~.
This is ==highlight with **bold** inside==.`;
      const result = markdownParse.parse(content);
      expect(result).toContain('<strong>');
      expect(result).toContain('<em>');
      expect(result).toContain('<code>');
      expect(result).toContain('<s>');
      expect(result).toContain('<mark>');
    });

    it('should handle links with various formats', () => {
      const content = `[Simple link](http://example.com)
[Link with title](http://example.com "Title")
[Link with **bold** text](http://example.com)
<http://autolink.com>
<mailto:<EMAIL>>
https://naked-url.com
[Reference link][1]
[Another reference][ref]

[1]: http://reference1.com
[ref]: http://reference2.com "Reference with title"`;
      const result = markdownParse.parse(content);
      expect(result).toContain('<a href="http://example.com">');
      expect(result).toContain('<a href="http://autolink.com">');
      expect(result).toContain('<a href="mailto:<EMAIL>">');
      expect(result).toContain('<a href="https://naked-url.com">');
      expect(result).toContain('<strong>bold</strong>');
    });

    it('should handle images with complex alt text and links', () => {
      const content = `![Simple image](image.jpg)
![Image with **bold** alt text](image.png)
![Complex alt with *italic* and \`code\`](image.gif)
[![Linked image](thumb.jpg)](full-size.jpg)
![SVG image](diagram.svg)
![Audio file](sound.mp3)
![Video file](video.mp4)`;
      const result = markdownParse.parse(content);
      expect(result).toContain('src="image.jpg"');
      expect(result).toContain('data-type="SvgEditor"');
      expect(result).toContain('data-type="Audio"');
      expect(result).toContain('<a href="full-size.jpg">');
    });

    it('should handle horizontal rules with surrounding content', () => {
      const content = `# Section 1
Some content here.

---

# Section 2
More content.

***

# Section 3
Final content.

___`;
      const result = markdownParse.parse(content);
      expect(result).toContain('<hr>');
      expect((result.match(/<hr>/g) || []).length).toBe(3);
      expect(result).toContain('<h1>Section 1</h1>');
      expect(result).toContain('<h1>Section 2</h1>');
      expect(result).toContain('<h1>Section 3</h1>');
    });
  });

  describe('Edge Cases and Special Content', () => {
    it('should handle empty lines and whitespace variations', () => {
      const content = `Paragraph 1


Paragraph 2 with trailing spaces   


   Paragraph 3 with leading spaces

${EMPTY_PARAGRAPH_NODE_MARKDOWN_VAL}

Final paragraph`;
      const result = markdownParse.parse(content);
      expect(result).toContain('<p>Paragraph 1</p>');
      expect(result).toContain('<p>Paragraph 2 with trailing spaces</p>');
      expect(result).toContain('<p>Paragraph 3 with leading spaces</p>');
      expect(result).toContain('<p></p>'); // Empty paragraph
      expect(result).toContain('<p>Final paragraph</p>');
    });

    it('should handle code blocks with special characters', () => {
      const content = `\`\`\`html
<script>
  const message = "Hello & <world>";
  document.write('<div>' + message + '</div>');
</script>
\`\`\`

\`\`\`json
{
  "key": "value with \"quotes\"",
  "special": "<>&",
  "unicode": "测试 🚀"
}
\`\`\`

\`\`\`sql
SELECT * FROM users WHERE name = 'O''Reilly' AND age > 18;
\`\`\``;
      const result = markdownParse.parse(content);
      expect(result).toContain('<pre class="html">');
      expect(result).toContain('<pre class="json">');
      expect(result).toContain('<pre class="sql">');
      expect(result).toContain('Hello & <world>');
      expect(result).toContain('O\'\'Reilly');
    });

    it('should handle multiple consecutive formatting', () => {
      const content = `**Bold1** **Bold2** **Bold3**
*Italic1* *Italic2* *Italic3*
\`code1\` \`code2\` \`code3\`
~~strike1~~ ~~strike2~~ ~~strike3~~
==mark1== ==mark2== ==mark3==
[Link1](url1) [Link2](url2) [Link3](url3)`;
      const result = markdownParse.parse(content);
      expect((result.match(/<strong>/g) || []).length).toBe(3);
      expect((result.match(/<em>/g) || []).length).toBe(3);
      expect((result.match(/<code>/g) || []).length).toBe(3);
      expect((result.match(/<s>/g) || []).length).toBe(3);
      expect((result.match(/<mark>/g) || []).length).toBe(3);
    });

    it('should handle malformed and edge case markup', () => {
      const content = `**Unclosed bold
*Unclosed italic
\`Unclosed code
~~Unclosed strike
==Unclosed mark
[Unclosed link](
![Unclosed image](
> Unclosed quote

***Bold with stars***
___Underscores___
Mixed **bold and *italic** and more*
Escaped \\**not bold\\** and \\*not italic\\*`;
      const result = markdownParse.parse(content);
      expect(result).toBeDefined();
      expect(result.length).toBeGreaterThan(0);
      expect(result).toContain('Unclosed');
    });

    it('should handle very long content with mixed elements', () => {
      const longText = 'This is a very long paragraph '.repeat(50);
      const content = `# Long Content Test

${longText}

- List item with ${longText.substring(0, 100)}...
- Another item

\`\`\`javascript
// Long code block
${'console.log("line");'.repeat(20)}
\`\`\`

> Long blockquote: ${longText}

![Long alt text: ${longText.substring(0, 200)}...](image.jpg)`;
      const result = markdownParse.parse(content);
      expect(result).toContain('<h1>Long Content Test</h1>');
      expect(result).toContain('<ul>');
      expect(result).toContain('<pre class="javascript">');
      expect(result).toContain('<blockquote>');
      expect(result.length).toBeGreaterThan(1000);
    });
  });

  describe('Performance and Stress Tests', () => {
    it('should handle deeply nested structures', () => {
      let content = '';
      for (let i = 0; i < 10; i++) {
        content += '  '.repeat(i) + `- Level ${i + 1}\n`;
      }
      const result = markdownParse.parse(content);
      expect(result).toContain('<ul>');
      expect(result).toContain('Level 1');
      expect(result).toContain('Level 10');
    });

    it('should handle many repeated elements', () => {
      const items = Array.from({ length: 100 }, (_, i) => `- Item ${i + 1}`).join('\n');
      const result = markdownParse.parse(items);
      expect(result).toContain('<ul>');
      expect(result).toContain('Item 1');
      expect(result).toContain('Item 100');
      expect((result.match(/<li>/g) || []).length).toBe(100);
    });

    it('should handle mixed content with many elements', () => {
      const sections = Array.from({ length: 20 }, (_, i) => 
        `## Section ${i + 1}
        
Paragraph with **bold** and *italic* text.

- List item 1
- List item 2

\`\`\`javascript
const section${i + 1} = true;
\`\`\`

> Quote for section ${i + 1}
`).join('\n\n');
      
      const result = markdownParse.parse(sections);
      expect(result).toContain('<h2>Section 1</h2>');
      expect(result).toContain('<h2>Section 20</h2>');
      expect((result.match(/<h2>/g) || []).length).toBe(20);
      expect((result.match(/<ul>/g) || []).length).toBe(20);
    });
  });
});