/* tslint:disable */
/* eslint-disable */
/**
 * You<PERSON><PERSON> (Snake Case)
 *   This documentation uses snake_case naming convention for compatibility with legacy clients.
 *
 * The version of the OpenAPI document: 1.0
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../../../runtime';
import type { CreditTransactionDto } from './CreditTransactionDto';
import {
  CreditTransactionDtoFromJSON,
  CreditTransactionDtoFromJSONTyped,
  CreditTransactionDtoToJSON,
  CreditTransactionDtoToJSONTyped,
} from './CreditTransactionDto';

/**
 *
 * @export
 * @interface ConsumeTransactionsInCurrentPeriodDto
 */
export interface ConsumeTransactionsInCurrentPeriodDto {
  /**
   * 当前周期内的积分消费记录
   * @type {Array<CreditTransactionDto>}
   * @memberof ConsumeTransactionsInCurrentPeriodDto
   */
  transactions: Array<CreditTransactionDto>;
  /**
   * 当前周期开始时间
   * @type {Date}
   * @memberof ConsumeTransactionsInCurrentPeriodDto
   */
  current_period_start: Date;
  /**
   * 当前周期结束时间
   * @type {Date}
   * @memberof ConsumeTransactionsInCurrentPeriodDto
   */
  current_period_end: Date;
}

/**
 * Check if a given object implements the ConsumeTransactionsInCurrentPeriodDto interface.
 */
export function instanceOfConsumeTransactionsInCurrentPeriodDto(
  value: object,
): value is ConsumeTransactionsInCurrentPeriodDto {
  if (!('transactions' in value) || value.transactions === undefined) return false;
  if (!('current_period_start' in value) || value.current_period_start === undefined) return false;
  if (!('current_period_end' in value) || value.current_period_end === undefined) return false;
  return true;
}

export function ConsumeTransactionsInCurrentPeriodDtoFromJSON(
  json: any,
): ConsumeTransactionsInCurrentPeriodDto {
  return ConsumeTransactionsInCurrentPeriodDtoFromJSONTyped(json, false);
}

export function ConsumeTransactionsInCurrentPeriodDtoFromJSONTyped(
  json: any,
  _ignoreDiscriminator: boolean,
): ConsumeTransactionsInCurrentPeriodDto {
  if (json == null) {
    return json;
  }
  return {
    transactions: (json.transactions as Array<any>).map(CreditTransactionDtoFromJSON),
    current_period_start: new Date(json.current_period_start),
    current_period_end: new Date(json.current_period_end),
  };
}

export function ConsumeTransactionsInCurrentPeriodDtoToJSON(
  json: any,
): ConsumeTransactionsInCurrentPeriodDto {
  return ConsumeTransactionsInCurrentPeriodDtoToJSONTyped(json, false);
}

export function ConsumeTransactionsInCurrentPeriodDtoToJSONTyped(
  value?: ConsumeTransactionsInCurrentPeriodDto | null,
  _ignoreDiscriminator: boolean = false,
): any {
  if (value == null) {
    return value;
  }

  return {
    transactions: (value.transactions as Array<any>).map(CreditTransactionDtoToJSON),
    current_period_start: value.current_period_start.toISOString(),
    current_period_end: value.current_period_end.toISOString(),
  };
}
