/* tslint:disable */
/* eslint-disable */
/**
 * You<PERSON><PERSON> (Snake Case)
 *   This documentation uses snake_case naming convention for compatibility with legacy clients.
 *
 * The version of the OpenAPI document: 1.0
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import type { AssistantMessageV1Dto } from './AssistantMessageV1Dto';
import {
  AssistantMessageV1DtoFromJSON,
  AssistantMessageV1DtoFromJSONTyped,
  AssistantMessageV1DtoToJSON,
  instanceOfAssistantMessageV1Dto,
} from './AssistantMessageV1Dto';
import type { UserMessageV1Dto } from './UserMessageV1Dto';
import {
  instanceOfUserMessageV1Dto,
  UserMessageV1DtoFromJSON,
  UserMessageV1DtoFromJSONTyped,
  UserMessageV1DtoToJSON,
} from './UserMessageV1Dto';
/**
 * @type ChatDetailV1DtoMessages
 * Chat messages
 * @export
 */
export type ChatDetailV1DtoMessages = AssistantMessageV1Dto | UserMessageV1Dto;

export function ChatDetailV1DtoMessagesFromJSON(json: any): ChatDetailV1DtoMessages {
  return ChatDetailV1DtoMessagesFromJSONTyped(json, false);
}

export function ChatDetailV1DtoMessagesFromJSONTyped(
  json: any,
  _ignoreDiscriminator: boolean,
): ChatDetailV1DtoMessages {
  if (json == null) {
    return json;
  }

  switch (json.$class) {
    case 'AssistantMessageV1Dto':
      return AssistantMessageV1DtoFromJSONTyped(json, true);
    case 'UserMessageV1Dto':
      return UserMessageV1DtoFromJSONTyped(json, true);
    default:
      return json;
  }
}

export function ChatDetailV1DtoMessagesToJSON(json: any): any {
  return ChatDetailV1DtoMessagesToJSONTyped(json, false);
}

export function ChatDetailV1DtoMessagesToJSONTyped(
  value?: any,
  _ignoreDiscriminator: boolean = false,
): any {
  if (value == null) {
    return value;
  }

  // First try to use discriminator property if it exists
  if (value.$class) {
    switch (value.$class) {
      case 'AssistantMessageV1Dto':
        return AssistantMessageV1DtoToJSON(value);
      case 'UserMessageV1Dto':
        return UserMessageV1DtoToJSON(value);
      default:
        // Fall through to instanceOf checks
        break;
    }
  }

  // Fallback to instanceOf checks
  if (instanceOfAssistantMessageV1Dto(value)) {
    return AssistantMessageV1DtoToJSON(value);
  }
  if (instanceOfUserMessageV1Dto(value)) {
    return UserMessageV1DtoToJSON(value);
  }

  return {};
}
