/* tslint:disable */
/* eslint-disable */
/**
 * <PERSON><PERSON><PERSON> (Snake Case)
 *   This documentation uses snake_case naming convention for compatibility with legacy clients.
 *
 * The version of the OpenAPI document: 1.0
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import type { ContentBlockV2Dto } from './ContentBlockV2Dto';
import {
  ContentBlockV2DtoFromJSON,
  ContentBlockV2DtoFromJSONTyped,
  ContentBlockV2DtoToJSON,
  instanceOfContentBlockV2Dto,
} from './ContentBlockV2Dto';
import type { ReasoningBlockV2Dto } from './ReasoningBlockV2Dto';
import {
  instanceOfReasoningBlockV2Dto,
  ReasoningBlockV2DtoFromJSON,
  ReasoningBlockV2DtoFromJSONTyped,
  ReasoningBlockV2DtoToJSON,
} from './ReasoningBlockV2Dto';
import type { ToolBlockV2Dto } from './ToolBlockV2Dto';
import {
  instanceOfToolBlockV2Dto,
  ToolBlockV2DtoFromJSON,
  ToolBlockV2DtoFromJSONTyped,
  ToolBlockV2DtoToJSON,
} from './ToolBlockV2Dto';
/**
 * @type AssistantMessageV2DtoBlocks
 * Completion blocks containing content, reasoning, and tool calls
 * @export
 */
export type AssistantMessageV2DtoBlocks = ContentBlockV2Dto | ReasoningBlockV2Dto | ToolBlockV2Dto;

export function AssistantMessageV2DtoBlocksFromJSON(json: any): AssistantMessageV2DtoBlocks {
  return AssistantMessageV2DtoBlocksFromJSONTyped(json, false);
}

export function AssistantMessageV2DtoBlocksFromJSONTyped(
  json: any,
  _ignoreDiscriminator: boolean,
): AssistantMessageV2DtoBlocks {
  if (json == null) {
    return json;
  }

  switch (json.$class) {
    case 'ContentBlockV2Dto':
      return ContentBlockV2DtoFromJSONTyped(json, true);
    case 'ReasoningBlockV2Dto':
      return ReasoningBlockV2DtoFromJSONTyped(json, true);
    case 'ToolBlockV2Dto':
      return ToolBlockV2DtoFromJSONTyped(json, true);
    default:
      return json;
  }
}

export function AssistantMessageV2DtoBlocksToJSON(json: any): any {
  return AssistantMessageV2DtoBlocksToJSONTyped(json, false);
}

export function AssistantMessageV2DtoBlocksToJSONTyped(
  value?: any,
  _ignoreDiscriminator: boolean = false,
): any {
  if (value == null) {
    return value;
  }

  // First try to use discriminator property if it exists
  if (value.$class) {
    switch (value.$class) {
      case 'ContentBlockV2Dto':
        return ContentBlockV2DtoToJSON(value);
      case 'ReasoningBlockV2Dto':
        return ReasoningBlockV2DtoToJSON(value);
      case 'ToolBlockV2Dto':
        return ToolBlockV2DtoToJSON(value);
      default:
        // Fall through to instanceOf checks
        break;
    }
  }

  // Fallback to instanceOf checks
  if (instanceOfContentBlockV2Dto(value)) {
    return ContentBlockV2DtoToJSON(value);
  }
  if (instanceOfReasoningBlockV2Dto(value)) {
    return ReasoningBlockV2DtoToJSON(value);
  }
  if (instanceOfToolBlockV2Dto(value)) {
    return ToolBlockV2DtoToJSON(value);
  }

  return {};
}
