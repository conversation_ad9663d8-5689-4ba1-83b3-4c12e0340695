/* tslint:disable */
/* eslint-disable */
/**
 * YouAPI
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 1.0
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import * as runtime from '../../../runtime';
import type {
  AdvanceTestClockDto,
  ConsumeCreditsDto,
  ConsumeCreditsResponseDto,
  ConsumeTransactionsInCurrentPeriodDto,
  CreditAccountDto,
} from '../models/index';
import {
  AdvanceTestClockDtoFromJSON,
  AdvanceTestClockDtoToJSON,
  ConsumeCreditsDtoFromJSON,
  ConsumeCreditsDtoToJSON,
  ConsumeCreditsResponseDtoFromJSON,
  ConsumeCreditsResponseDtoToJSON,
  ConsumeTransactionsInCurrentPeriodDtoFromJSON,
  ConsumeTransactionsInCurrentPeriodDtoToJSON,
  CreditAccountDtoFromJSON,
  CreditAccountDtoToJSON,
} from '../models/index';

export interface CreditControllerAdvanceTestClockRequest {
  advanceTestClockDto: AdvanceTestClockDto;
}

export interface CreditControllerConsumeCreditsRequest {
  consumeCreditsDto: ConsumeCreditsDto;
}

/**
 * CreditApi - interface
 *
 * @export
 * @interface CreditApiInterface
 */
export interface CreditApiInterface {
  /**
   * 用于测试环境中控制当前用户积分账户的 Stripe 测试时钟时间，推进到指定日期时间
   * @summary 将测试时钟推进到指定时间
   * @param {AdvanceTestClockDto} advanceTestClockDto
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof CreditApiInterface
   */
  advanceTestClockRaw(
    requestParameters: CreditControllerAdvanceTestClockRequest,
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<runtime.ApiResponse<CreditAccountDto>>;

  /**
   * 用于测试环境中控制当前用户积分账户的 Stripe 测试时钟时间，推进到指定日期时间
   * 将测试时钟推进到指定时间
   */
  advanceTestClock(
    advanceTestClockDto: AdvanceTestClockDto,
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<CreditAccountDto>;

  /**
   * 从当前用户的积分账户中消费指定数量的积分
   * @summary 消费指定数量的积分
   * @param {ConsumeCreditsDto} consumeCreditsDto
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof CreditApiInterface
   */
  consumeCreditsRaw(
    requestParameters: CreditControllerConsumeCreditsRequest,
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<runtime.ApiResponse<ConsumeCreditsResponseDto>>;

  /**
   * 从当前用户的积分账户中消费指定数量的积分
   * 消费指定数量的积分
   */
  consumeCredits(
    consumeCreditsDto: ConsumeCreditsDto,
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<ConsumeCreditsResponseDto>;

  /**
   * 查询当前用户的积分账户
   * @summary 查询当前用户的积分账户
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof CreditApiInterface
   */
  getCreditAccountRaw(
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<runtime.ApiResponse<CreditAccountDto>>;

  /**
   * 查询当前用户的积分账户
   * 查询当前用户的积分账户
   */
  getCreditAccount(
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<CreditAccountDto>;

  /**
   * 查询当前用户在当前周期内的所有积分消费记录
   * @summary 查询当前周期内的积分消费记录
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof CreditApiInterface
   */
  listCreditsConsumeTransactionsInCurrentPeriodRaw(
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<runtime.ApiResponse<ConsumeTransactionsInCurrentPeriodDto>>;

  /**
   * 查询当前用户在当前周期内的所有积分消费记录
   * 查询当前周期内的积分消费记录
   */
  listCreditsConsumeTransactionsInCurrentPeriod(
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<ConsumeTransactionsInCurrentPeriodDto>;
}

/**
 *
 */
export class CreditApi extends runtime.BaseAPI implements CreditApiInterface {
  /**
   * 用于测试环境中控制当前用户积分账户的 Stripe 测试时钟时间，推进到指定日期时间
   * 将测试时钟推进到指定时间
   */
  async advanceTestClockRaw(
    requestParameters: CreditControllerAdvanceTestClockRequest,
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<runtime.ApiResponse<CreditAccountDto>> {
    if (requestParameters.advanceTestClockDto == null) {
      throw new runtime.RequiredError(
        'advanceTestClockDto',
        'Required parameter "advanceTestClockDto" was null or undefined when calling advanceTestClock().',
      );
    }

    const queryParameters: any = {};

    const headerParameters: runtime.HTTPHeaders = {};

    headerParameters['Content-Type'] = 'application/json';

    const urlPath = `/api/v1/credit/advanceTestClock`;

    const response = await this.request(
      {
        path: urlPath,
        method: 'POST',
        headers: headerParameters,
        query: queryParameters,
        body: AdvanceTestClockDtoToJSON(requestParameters.advanceTestClockDto),
      },
      initOverrides,
    );

    return new runtime.JSONApiResponse(response, (jsonValue) =>
      CreditAccountDtoFromJSON(jsonValue),
    );
  }

  /**
   * 用于测试环境中控制当前用户积分账户的 Stripe 测试时钟时间，推进到指定日期时间
   * 将测试时钟推进到指定时间
   */
  async advanceTestClock(
    advanceTestClockDto: AdvanceTestClockDto,
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<CreditAccountDto> {
    const response = await this.advanceTestClockRaw(
      { advanceTestClockDto: advanceTestClockDto },
      initOverrides,
    );
    return await response.value();
  }

  /**
   * 从当前用户的积分账户中消费指定数量的积分
   * 消费指定数量的积分
   */
  async consumeCreditsRaw(
    requestParameters: CreditControllerConsumeCreditsRequest,
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<runtime.ApiResponse<ConsumeCreditsResponseDto>> {
    if (requestParameters.consumeCreditsDto == null) {
      throw new runtime.RequiredError(
        'consumeCreditsDto',
        'Required parameter "consumeCreditsDto" was null or undefined when calling consumeCredits().',
      );
    }

    const queryParameters: any = {};

    const headerParameters: runtime.HTTPHeaders = {};

    headerParameters['Content-Type'] = 'application/json';

    const urlPath = `/api/v1/credit/consumeCredits`;

    const response = await this.request(
      {
        path: urlPath,
        method: 'POST',
        headers: headerParameters,
        query: queryParameters,
        body: ConsumeCreditsDtoToJSON(requestParameters.consumeCreditsDto),
      },
      initOverrides,
    );

    return new runtime.JSONApiResponse(response, (jsonValue) =>
      ConsumeCreditsResponseDtoFromJSON(jsonValue),
    );
  }

  /**
   * 从当前用户的积分账户中消费指定数量的积分
   * 消费指定数量的积分
   */
  async consumeCredits(
    consumeCreditsDto: ConsumeCreditsDto,
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<ConsumeCreditsResponseDto> {
    const response = await this.consumeCreditsRaw(
      { consumeCreditsDto: consumeCreditsDto },
      initOverrides,
    );
    return await response.value();
  }

  /**
   * 查询当前用户的积分账户
   * 查询当前用户的积分账户
   */
  async getCreditAccountRaw(
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<runtime.ApiResponse<CreditAccountDto>> {
    const queryParameters: any = {};

    const headerParameters: runtime.HTTPHeaders = {};

    const urlPath = `/api/v1/credit/getCreditAccount`;

    const response = await this.request(
      {
        path: urlPath,
        method: 'POST',
        headers: headerParameters,
        query: queryParameters,
      },
      initOverrides,
    );

    return new runtime.JSONApiResponse(response, (jsonValue) =>
      CreditAccountDtoFromJSON(jsonValue),
    );
  }

  /**
   * 查询当前用户的积分账户
   * 查询当前用户的积分账户
   */
  async getCreditAccount(
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<CreditAccountDto> {
    const response = await this.getCreditAccountRaw(initOverrides);
    return await response.value();
  }

  /**
   * 查询当前用户在当前周期内的所有积分消费记录
   * 查询当前周期内的积分消费记录
   */
  async listCreditsConsumeTransactionsInCurrentPeriodRaw(
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<runtime.ApiResponse<ConsumeTransactionsInCurrentPeriodDto>> {
    const queryParameters: any = {};

    const headerParameters: runtime.HTTPHeaders = {};

    const urlPath = `/api/v1/credit/listCreditsConsumeTransactionsInCurrentPeriod`;

    const response = await this.request(
      {
        path: urlPath,
        method: 'POST',
        headers: headerParameters,
        query: queryParameters,
      },
      initOverrides,
    );

    return new runtime.JSONApiResponse(response, (jsonValue) =>
      ConsumeTransactionsInCurrentPeriodDtoFromJSON(jsonValue),
    );
  }

  /**
   * 查询当前用户在当前周期内的所有积分消费记录
   * 查询当前周期内的积分消费记录
   */
  async listCreditsConsumeTransactionsInCurrentPeriod(
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<ConsumeTransactionsInCurrentPeriodDto> {
    const response = await this.listCreditsConsumeTransactionsInCurrentPeriodRaw(initOverrides);
    return await response.value();
  }
}
