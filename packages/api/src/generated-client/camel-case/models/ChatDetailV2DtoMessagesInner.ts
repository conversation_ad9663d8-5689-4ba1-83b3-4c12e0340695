/* tslint:disable */
/* eslint-disable */
/**
 * YouAPI
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 1.0
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import type { AssistantMessageV2Dto } from './AssistantMessageV2Dto';
import {
  AssistantMessageV2DtoFromJSON,
  AssistantMessageV2DtoFromJSONTyped,
  AssistantMessageV2DtoToJSON,
  instanceOfAssistantMessageV2Dto,
} from './AssistantMessageV2Dto';
import type { UserMessageV2Dto } from './UserMessageV2Dto';
import {
  instanceOfUserMessageV2Dto,
  UserMessageV2DtoFromJSON,
  UserMessageV2DtoFromJSONTyped,
  UserMessageV2DtoToJSON,
} from './UserMessageV2Dto';
/**
 * @type ChatDetailV2DtoMessagesInner
 *
 * @export
 */
export type ChatDetailV2DtoMessagesInner = AssistantMessageV2Dto | UserMessageV2Dto;

export function ChatDetailV2DtoMessagesInnerFromJSON(json: any): ChatDetailV2DtoMessagesInner {
  return ChatDetailV2DtoMessagesInnerFromJSONTyped(json, false);
}

export function ChatDetailV2DtoMessagesInnerFromJSONTyped(
  json: any,
  _ignoreDiscriminator: boolean,
): ChatDetailV2DtoMessagesInner {
  if (json == null) {
    return json;
  }

  switch (json.$class) {
    case 'AssistantMessageV2Dto':
      return AssistantMessageV2DtoFromJSONTyped(json, true);
    case 'UserMessageV2Dto':
      return UserMessageV2DtoFromJSONTyped(json, true);
    default:
      return json;
  }
}

export function ChatDetailV2DtoMessagesInnerToJSON(json: any): any {
  return ChatDetailV2DtoMessagesInnerToJSONTyped(json, false);
}

export function ChatDetailV2DtoMessagesInnerToJSONTyped(
  value?: any,
  _ignoreDiscriminator: boolean = false,
): any {
  if (value == null) {
    return value;
  }

  // First try to use discriminator property if it exists
  if (value.$class) {
    switch (value.$class) {
      case 'AssistantMessageV2Dto':
        return AssistantMessageV2DtoToJSON(value);
      case 'UserMessageV2Dto':
        return UserMessageV2DtoToJSON(value);
      default:
        // Fall through to instanceOf checks
        break;
    }
  }

  // Fallback to instanceOf checks
  if (instanceOfAssistantMessageV2Dto(value)) {
    return AssistantMessageV2DtoToJSON(value);
  }
  if (instanceOfUserMessageV2Dto(value)) {
    return UserMessageV2DtoToJSON(value);
  }

  return {};
}
