import {
  DIFF_CHANGE_TYPE,
  diffTransformUtils,
  markdownParse,
  markdownSerializer,
} from '@repo/editor-common';
import { Extension } from '@tiptap/core';
import { DOMParser } from '@tiptap/pm/model';
import { Plugin, PluginKey } from '@tiptap/pm/state';
import { isMobile } from '../thought/utils';

export const ClipboardAdapterExtensionName = 'clipboardAdapter';

declare module '@tiptap/core' {
  interface Commands<ReturnType> {
    clipboardAdapter: {
      copyExternalMarkdownContent: () => ReturnType;
      copyPlainTextContent: () => ReturnType;
    };
  }
}

interface ClipboardData {
  [key: string]: string;
}

export async function writeToClipboard(data: ClipboardData): Promise<void> {
  // 使用现代的 Clipboard API，支持自定义键值对
  if (navigator.clipboard && window.isSecureContext) {
    try {
      const items: ClipboardItem[] = [];
      const clipboardData: Record<string, Blob> = {};

      Object.entries(data).forEach(([mimeType, content]) => {
        clipboardData[mimeType] = new Blob([content], { type: mimeType });
      });

      items.push(new ClipboardItem(clipboardData));
      await navigator.clipboard.write(items);
      return;
    } catch (error) {
      console.warn('Modern clipboard API failed:', error);
      throw new Error('Clipboard operation failed: Modern API not available or permission denied');
    }
  }

  // 如果不支持现代 Clipboard API，抛出错误
  throw new Error(
    'Clipboard operation failed: Modern Clipboard API not supported in current context',
  );
}

/**
 * 改进的 Markdown 检测：更严格的判断，减少误判
 */
function isDefinitelyMarkdown(text: string): boolean {
  if (!text || text.trim().length < 10) return false;

  const lines = text.split(/\r?\n/);
  let strongIndicators = 0;

  // 强 Markdown 指示符（单个即可判断）
  if (lines[0] && /^#{1,6}\s+\S/.test(lines[0])) return true; // 开头是标题
  if (/```[\s\S]*?```/.test(text)) return true; // 有代码块围栏
  if (lines.some((l) => /^(?:\s{0,3})(?:[-*+]\s+|\d+\.\s+)/.test(l)) && lines.length > 2) {
    strongIndicators += 1; // 有列表且行数足够
  }
  if (
    /^\|.*\|.*$/m.test(text) &&
    /^\|?\s*:?[-]{3,}:?\s*(\|\s*:?[-]{3,}:?\s*)+\|?\s*$/m.test(text)
  ) {
    return true; // 有明确的表格格式
  }

  // 弱指示符需要累积判断
  if (lines.some((l) => /^>\s+/.test(l))) strongIndicators += 0.5;
  if (/!\[[^\]]*\]\([^)]+\)|\[[^\]]+\]\([^)]+\)/.test(text)) strongIndicators += 0.5;
  if (/^\s{0,3}(?:-\s?){3,}$|^\s{0,3}(?:\*\s?){3,}$|^\s{0,3}(?:_\s?){3,}$/m.test(text))
    strongIndicators += 0.5;

  return strongIndicators >= 1.5;
}

export const ClipboardAdapter = Extension.create({
  name: ClipboardAdapterExtensionName,

  addCommands() {
    return {
      copyExternalMarkdownContent:
        () =>
        ({ state }) => {
          const doc = state.doc;

          const oldContent = diffTransformUtils.extractContent(doc, DIFF_CHANGE_TYPE.REMOVED);

          // 使用 markdownSerializer 将内容序列化为 Markdown
          const markdown = markdownSerializer.externalCustomSerialize(oldContent);

          // 写入剪贴板
          writeToClipboard({
            'text/plain': markdown,
            'text/markdown': markdown,
          }).catch((error) => {
            console.error('Failed to copy markdown content:', error);
          });

          return true;
        },
      copyPlainTextContent:
        () =>
        ({ editor }) => {
          // 获取编辑器的纯文本内容
          const plainText = editor.getText();

          // 写入剪贴板
          writeToClipboard({
            'text/plain': plainText,
          }).catch((error) => {
            console.error('Failed to copy plain text content:', error);
          });

          return true;
        },
    };
  },

  addStorage() {
    return {
      markdown: {
        enabled: true,
      },
    };
  },

  addProseMirrorPlugins() {
    return [
      new Plugin({
        key: new PluginKey('clipboardAdapter'),
        props: {
          // 简化复制处理 - 使用 ProseMirror 原生的 data-pm-slice 机制
          transformCopied: (slice) => {
            // ProseMirror 会自动在复制的 HTML 中加入 data-pm-slice 标记
            return slice;
          },

          // 拦截移动端 copy 事件，按需写入自定义 Markdown
          handleDOMEvents: {
            copy: (view, event) => {
              try {
                if (!isMobile()) return false;

                const clipboardEvent = event as ClipboardEvent;
                const doc = view.state.doc;
                const oldContent = diffTransformUtils.extractContent(doc, DIFF_CHANGE_TYPE.REMOVED);

                const markdown = markdownSerializer.externalCustomSerialize(oldContent);
                // 仅移除普通空格与不间断空格，保留换行与制表符
                const cleanedMarkdown = markdown.replace(/[ \u00A0]+/g, '');

                if (clipboardEvent.clipboardData) {
                  clipboardEvent.clipboardData.setData('text/plain', cleanedMarkdown);
                  clipboardEvent.clipboardData.setData('text/markdown', cleanedMarkdown);
                  clipboardEvent.preventDefault();
                  return true;
                }

                // 兜底：若无 clipboardData，尝试使用异步写入
                writeToClipboard({
                  'text/plain': cleanedMarkdown,
                  'text/markdown': cleanedMarkdown,
                }).catch(() => {
                  // 忽略兜底失败，交还默认处理
                });

                // 不阻止默认，以便浏览器继续其默认复制流程
                return false;
              } catch (error) {
                console.error('Mobile copy handling failed:', error);
                return false;
              }
            },
          },

          // 简化的粘贴处理逻辑
          handlePaste: (view, event) => {
            const clipboardData = event.clipboardData;
            if (!clipboardData) return false;

            const html = clipboardData.getData('text/html');
            const text = clipboardData.getData('text/plain');

            // 优先级1: 如果包含 ProseMirror 切片标记，完全走原生逻辑（内部复制）
            // ProseMirror 原生会在内部复制时添加 data-pm-slice 注释
            if (html && /data-pm-slice/.test(html)) {
              return false;
            }

            // 优先级2: 如果有结构化的 HTML 内容，走原生逻辑（保持表格、列表等）
            if (html && /<(?:table|ul|ol|pre|blockquote|h[1-6]|img|li|tr|td|th)\b/i.test(html)) {
              return false;
            }

            // 优先级3: 只有纯文本且明确是 Markdown 时才特殊处理
            if (
              text &&
              (!html || html.trim().length <= text.length + 50) &&
              isDefinitelyMarkdown(text)
            ) {
              try {
                const parsedHtml = markdownParse.parse(text);
                const container = document.createElement('div');
                container.innerHTML = parsedHtml;

                const parser = DOMParser.fromSchema(view.state.schema);
                const slice = parser.parseSlice(container);

                const tr = view.state.tr.replaceSelection(slice);
                view.dispatch(tr);
                return true; // 阻止默认处理
              } catch (error) {
                console.error('Markdown parse failed:', error);
                // 解析失败，走原生逻辑
                return false;
              }
            }

            // 其他情况都走原生处理
            return false;
          },
        },
      }),
    ];
  },
});
