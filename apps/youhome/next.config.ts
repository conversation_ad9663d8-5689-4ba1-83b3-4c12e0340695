import type { NextConfig } from 'next';
import createNextIntlPlugin from 'next-intl/plugin';

const withNextIntl = createNextIntlPlugin();
const nextConfig: NextConfig = {
  eslint: {
    dirs: ['src'],
  },
  transpilePackages: ['@repo/ui'],
  poweredByHeader: false,
  images: {
    remotePatterns: [
      {
        protocol: 'https',
        hostname: 'cdn.gooo.ai',
        port: '',
        pathname: '/**',
      },
      {
        protocol: 'https',
        hostname: 'pbs.twimg.com',
        port: '',
        pathname: '/**',
      },
      {
        protocol: 'https',
        hostname: 'abs.twimg.com',
        port: '',
        pathname: '/**',
      },
    ],
  },
  // 开发环境代理配置
  ...(process.env.NODE_ENV === 'development' && {
    async rewrites() {
      return [
        {
          source: '/api/:path*',
          destination: 'http://localhost:4000/api/:path*',
        },
        {
          source: '/auth/:path*',
          destination: 'http://localhost:4000/auth/:path*',
        },
        {
          source: '/innerapi/:path*',
          destination: 'http://localhost:4000/innerapi/:path*',
        },
      ];
    },
    async redirects() {
      return [
        {
          source: '/boards/:path*',
          destination: 'http://localhost:2000/boards/:path*',
          permanent: true,
        },
      ];
    },
  }),
};

export default withNextIntl(nextConfig);

const useCloudflareBinding = !!process.env.CLOUDFLARE_ACCOUNT_ID;

// Initialize OpenNext for local development
if (process.env.NODE_ENV === 'development') {
  try {
    const { initOpenNextCloudflareForDev } = require('@opennextjs/cloudflare');
    initOpenNextCloudflareForDev({
      experimental: { remoteBindings: useCloudflareBinding },
    });
  } catch (error: any) {
    console.warn('OpenNext Cloudflare dev initialization failed:', error.message);
  }
}
