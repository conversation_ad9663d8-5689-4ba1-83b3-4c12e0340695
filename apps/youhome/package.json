{"name": "youhome", "version": "0.1.0", "private": true, "scripts": {"dev": "YOUMIND_ENV=preview ../../devops/scripts/with-doppler next dev -p 3001", "dev:cf": "YOUMIND_ENV=preview CLOUDFLARE_ACCOUNT_ID=ce1ed653c688afb19ad455cde3ba278a ../../devops/scripts/with-doppler next dev -p 3001", "dev:prod": "YOUMIND_ENV=production ../../devops/scripts/with-doppler next dev -p 3001", "build": "../../devops/scripts/with-doppler next build", "build:cf": "../../devops/scripts/with-doppler npx @opennextjs/cloudflare build", "test-echo": "echo 'Test echo works!'", "start": "YOUMIND_ENV=production ../../devops/scripts/with-doppler next start", "build:worker": "pnpm opennextjs-cloudflare build --env preview", "preview:worker": "pnpm opennextjs-cloudflare preview", "preview": "pnpm build:worker && pnpm preview:worker", "deploy": "wrangler deploy --env production", "lint": "biome check . --diagnostic-level=error", "lint:fix": "biome check . --write --diagnostic-level=error", "format": "biome format . --write", "format:check": "biome format .", "typecheck": "tsc --noEmit", "env": "node ../../devops/scripts/doppler-pull.js youhome", "env:prod": "node ../../devops/scripts/doppler-pull.js youhome --env=production", "config": "node ../../devops/scripts/doppler-pull-config.js", "config:prod": "node ../../devops/scripts/doppler-pull-config.js --env=production"}, "dependencies": {"@ai-sdk/google-vertex": "^2.2.27", "@cloudflare/stream-react": "^1.9.3", "@hookform/resolvers": "^3.10.0", "@payloadcms/richtext-lexical": "^3.46.0", "@radix-ui/react-tabs": "^1.1.12", "@repo/api": "workspace:*", "@repo/common": "workspace:*", "@repo/ui": "workspace:*", "@sentry/nextjs": "^9.37.0", "@youmindinc/youcommon": "^0.1.22", "@youmindinc/youicon": "catalog:", "ahooks": "catalog:", "clsx": "^2.1.1", "date-fns": "^4.1.0", "deepmerge": "^4.3.1", "embla-carousel-react": "^8.6.0", "franc-min": "^6.2.0", "hls.js": "^1.6.7", "html-to-image": "^1.11.13", "iso-639-3": "^3.0.1", "jotai": "catalog:", "lodash": "^4.17.21", "lucide-react": "catalog:", "marked": "^13.0.0", "next": "catalog:", "next-intl": "^4.3.4", "next-sitemap": "^4.2.3", "next-themes": "^0.4.6", "openai": "^5.8.2", "prism-react-renderer": "^2.4.1", "qs-esm": "^7.0.2", "react": "catalog:", "react-div-100vh": "^0.7.0", "react-dom": "catalog:", "react-hook-form": "catalog:", "server-only": "^0.0.1", "sonner": "^2.0.6", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "zod": "^3.24.1"}, "devDependencies": {"@opennextjs/cloudflare": "^1.6.0", "@types/lodash": "^4.17.20", "@types/node": "catalog:", "@types/react": "catalog:", "@types/react-dom": "catalog:", "autoprefixer": "^10.4.20", "eslint": "^8.57.1", "eslint-config-next": "15.1.3", "postcss": "^8.5.1", "tailwindcss": "catalog:", "typescript": "catalog:", "wrangler": "catalog:"}}