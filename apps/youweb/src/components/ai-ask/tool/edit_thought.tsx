import { TOOL_TYPES } from '@repo/common/consts/tool/const';
import { MessageStatusEnum } from '@repo/common/types/chat/enum';
import type { CompletionToolBlock } from '@repo/common/types/chat/types';
import { CompletionBlockStatusEnum } from '@repo/common/types/completion';
import type { ThoughtVO } from '@repo/common/types/thought/types';
import { Button } from '@repo/ui/components/ui/button';
import hljs from 'highlight.js';
import { useAtomValue, useSetAtom } from 'jotai';
import { ArrowUpRight } from 'lucide-react';
import { Marked, Renderer } from 'marked';
import { markedHighlight } from 'marked-highlight';
import markedKatex from 'marked-katex-extension';
import mermaid from 'mermaid';
import { useEffect, useRef, useState } from 'react';
import { CreateThought } from '@/components/icon/create-thought';
import { focusMaterialByEntityIdAtom, panelStateAtom } from '@/hooks/useBoardState';
import {
  boardDetailAtom,
  getBoardItemByEntityIdAtom,
  refreshBoardDetailAtom,
} from '@/hooks/useBoards';
import { thoughtEditorAtom } from '@/hooks/useThought';
import { formatSVG } from '@/utils/formatSVG';
import { cn } from '@/utils/utils';
import { ShineCard } from '../../shine-card';
import { ToolStatus } from './card';
import { useMessage } from './hooks/useMessage';
import { type TOOL_RENDERER, TOOL_SCENE_TYPE } from './type';
import './edit_thought.css';
import { CODEBLOCK_LANGUAGES } from '@repo/editor-common';
import { Thought } from '@/typings/thought';

const renderer = new Renderer();
const marked = new Marked(
  {
    ...markedHighlight({
      langPrefix: 'hljs language-',
      highlight(code, lang) {
        const language = hljs.getLanguage(lang) ? lang : 'plaintext';
        return hljs.highlight(code, { language }).value;
      },
    }),
    renderer: {
      code({ text, lang, raw }) {
        let preview = '';
        if (lang === 'mermaid') {
          const id = `mermaid-${Math.random().toString(36).substring(2, 15)}`;

          mermaid.initialize({
            startOnLoad: true,
            // @see https://github.com/mermaid-js/mermaid/issues/4358
            suppressErrorRendering: true,
          });

          // 在这里解码 HTML 实体，恢复原始的 mermaid 语法
          const decodedText = text
            .replace(/&gt;/g, '>')
            .replace(/&lt;/g, '<')
            .replace(/&amp;/g, '&');

          // @see https://github.com/mermaid-js/mermaid/issues/3227#issuecomment-2032574561
          setTimeout(async () => {
            try {
              const { svg } = await mermaid.render(`${id}-svg`, decodedText);
              const element = document.getElementById(id);
              if (element) {
                element.innerHTML = svg;
              }
            } catch (error) {
              console.error('Mermaid rendering error:', error);
            }
          }, 0);

          preview = `<div id="${id}" class="mermaid-container"></div>`;
        } else if (lang === 'xml' || lang === 'svg') {
          if (raw.includes('<svg')) {
            preview = `<div class="svg-container">${formatSVG(raw)}</div>`;
          }
        }

        return `
          <div class="code-block-container">
            ${lang ? `<div class="code-language">${CODEBLOCK_LANGUAGES.find((l) => l.value === lang)?.label}</div>` : ''}
            <div class="p-4 pt-0">
              <button class="ym-code-block-copy-button inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background disabled:pointer-events-none disabled:opacity-50 hover:bg-accent hover:text-accent-foreground z-1 absolute right-1 top-1 h-6 w-6 text-secondary-fg"><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-copy"><rect width="14" height="14" x="8" y="8" rx="2" ry="2"></rect><path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"></path></svg></button>
              <pre class="${preview ? 'hidden' : ''}"><code class="hljs language-${lang}">${text}</code></pre>
              ${preview}
            </div>
          </div>
        `;
      },
      table(...args) {
        return `
          <div class="px-6 rounded-lg  mx-2 my-4 bg-snip-card pb-1">
          ${renderer.table.apply(this, args)}
          </div>
        `;
      },
    },
  },
  markedKatex({ nonStandard: true, throwOnError: false }),
);

function getToolTitle(block: CompletionToolBlock) {
  let title = '';
  const { status } = block;
  const { thought_id } = block.tool_arguments as {
    thought_id?: string;
    title_edit?: string;
    text_edit?: string;
  };
  switch (status) {
    case CompletionBlockStatusEnum.ING:
      title = 'Writing...';
      break;
    case CompletionBlockStatusEnum.EXECUTING:
      title = 'Applying...';
      break;
    case CompletionBlockStatusEnum.DONE:
      if (!thought_id) {
        title = 'Created new thought';
      } else {
        title = 'Edited thought';
      }
      break;
    case CompletionBlockStatusEnum.ERROR:
      if (!thought_id) {
        title = 'Failed to create thought';
      } else {
        title = 'Failed to edit thought';
      }
      break;
    default:
      break;
  }
  return title || 'Writing...';
}
export const EditThoughtCard = ({
  block,
  variant,
  scene,
}: {
  block: CompletionToolBlock;
  variant: 'small' | 'middle';
  scene: TOOL_SCENE_TYPE;
}) => {
  const message = useMessage();
  const contentRef = useRef<HTMLDivElement>(null);
  const [isOverflowing, setIsOverflowing] = useState(false);
  const [isScrolledToBottom, setIsScrolledToBottom] = useState(false);

  const thoughtEditor = useAtomValue(thoughtEditorAtom);
  const refreshBoardDetail = useSetAtom(refreshBoardDetailAtom);
  const panelState = useAtomValue(panelStateAtom);
  const focusMaterialByEntityId = useSetAtom(focusMaterialByEntityIdAtom);

  const invokeThoughtUpdate = useRef(false);

  const { status } = block;
  const { thought_id, title_edit, text_edit } = block.tool_arguments as {
    thought_id?: string;
    title_edit?: string;
    text_edit?: string;
  };
  const toolResult = block.tool_result as ThoughtVO | undefined;
  const getBoardItemByEntityId = useAtomValue(getBoardItemByEntityIdAtom);
  const thoughtBoardItem = thought_id ? getBoardItemByEntityId(thought_id) : undefined;

  let title: React.ReactNode;
  // 考虑创建和编辑时最后打开的 thought 的 id
  const openThoughtId = thought_id || toolResult?.id;
  const boardDetail = useAtomValue(boardDetailAtom);
  const boardId = toolResult?.board_item?.board_id;
  const finalThoughtTitle =
    title_edit || (thoughtBoardItem?.entity as ThoughtVO | undefined)?.title || toolResult?.title;

  switch (status) {
    case CompletionBlockStatusEnum.ING:
      title = 'Writing...';
      break;
    case CompletionBlockStatusEnum.EXECUTING:
      title = 'Applying...';
      break;
    case CompletionBlockStatusEnum.DONE:
      if (!thought_id) {
        title = (
          <span>
            <span className="font-medium">Add: </span>
            {title_edit ?? ''}
          </span>
        );
      } else if (title_edit) {
        title = (
          <span>
            <span className="font-medium">Edit: </span>
            {title_edit ?? ''}
          </span>
        );
      } else {
        title = (
          <span>
            <span className="font-medium">Edit: </span>
            {toolResult?.title ?? ''}
          </span>
        );
      }
      break;
    case CompletionBlockStatusEnum.ERROR:
      if (!thought_id) {
        title = 'Failed to create thought';
      } else {
        title = 'Failed to edit thought';
      }
      break;
    default:
      break;
  }
  let finalTextEdit = text_edit;
  if (text_edit?.trim().startsWith('#')) {
    // 使用正则表达式只移除文章开头的 H1 标题
    finalTextEdit = text_edit.replace(/^\s*# .*?\n/, '');
    if (finalThoughtTitle && finalTextEdit.includes(finalThoughtTitle)) {
      // 使用正则表达式移除文章开头的 H1~H6 标题，但仅当标题内容与 finalThoughtTitle 完全一样时
      const headingRegex = new RegExp(`^\\s*(#{1,6})\\s+${finalThoughtTitle}\\s*\\n`, 'i');
      finalTextEdit = finalTextEdit.replace(headingRegex, '');
    }
  }

  // 移除图片的 @large, @medium, @small 后缀，避免 SVG 图片展示问题
  if (finalTextEdit) {
    finalTextEdit = removeImageSuffixes(finalTextEdit);
  }

  useEffect(() => {
    if (contentRef.current) {
      const element = contentRef.current;
      setIsOverflowing(element.scrollHeight > element.clientHeight);

      const checkIfScrolledToBottom = () => {
        if (element) {
          const isAtBottom =
            Math.abs(element.scrollHeight - element.clientHeight - element.scrollTop) < 2;
          setIsScrolledToBottom(isAtBottom);
        }
      };

      // 检查初始状态是否已经滚动到底部
      checkIfScrolledToBottom();

      // 添加滚动事件监听器
      const handleScroll = () => {
        checkIfScrolledToBottom();
      };

      element.addEventListener('scroll', handleScroll);
      return () => {
        element.removeEventListener('scroll', handleScroll);
      };
    }
  }, []);

  useEffect(() => {
    if (message?.status !== MessageStatusEnum.ING) {
      return;
    }
    // 只在操作执行完成，才处理 thought 的更新和呈现 diff
    if (status !== CompletionBlockStatusEnum.DONE || scene !== TOOL_SCENE_TYPE.CHAT) {
      return;
    }

    if (invokeThoughtUpdate.current) {
      return;
    }

    if (thoughtEditor && panelState.panelData?.entity?.id === block.tool_arguments.thought_id) {
      try {
        thoughtEditor.commands.setAIWriterContent(block.tool_result as Thought);
      } catch (e) {
        console.warn('thoughtEditor.commands.setAIWriterContent error', e);
      }
    } else {
      refreshBoardDetail();
    }
    invokeThoughtUpdate.current = true;
  }, [status, message?.status]);

  if (message?.status === MessageStatusEnum.ERROR && status !== CompletionBlockStatusEnum.DONE) {
    return null;
  }

  return (
    <ShineCard
      shine={
        status === CompletionBlockStatusEnum.ING || status === CompletionBlockStatusEnum.EXECUTING
      }
    >
      <div
        className={cn(
          'flex h-8 items-center justify-between border-b border-muted px-4 text-xs text-caption-fg',
        )}
      >
        <div
          className={cn(
            'truncate',
            status === CompletionBlockStatusEnum.ING ||
              status === CompletionBlockStatusEnum.EXECUTING
              ? 'loading-shimmer'
              : '',
          )}
        >
          {title}
        </div>
        {scene === TOOL_SCENE_TYPE.CHAT &&
        openThoughtId &&
        boardId &&
        status === CompletionBlockStatusEnum.DONE ? (
          <Button
            variant="ghost"
            size="sm"
            onClick={() => {
              if (boardDetail?.id === boardId) {
                focusMaterialByEntityId(openThoughtId);
              } else {
                window.open(
                  `/boards/${boardId}?entity-type=thought&entity-id=${openThoughtId}`,
                  '_blank',
                );
              }
            }}
          >
            <ArrowUpRight size={16} className="mr-1" />
            Open
          </Button>
        ) : null}
      </div>
      <div
        className={cn(
          'relative overflow-auto p-4',
          variant === 'small' ? 'max-h-[320px]' : 'max-h-[480px]',
        )}
        ref={contentRef}
      >
        <div className="text-[13px]">
          {title_edit && <div className="mb-[1em] text-[1.3em] font-semibold">{title_edit}</div>}
          {finalTextEdit && (
            <div
              className="ym-edit-thought-content ym-askai-content"
              dangerouslySetInnerHTML={{
                __html: marked.parse(finalTextEdit) as string,
              }}
            />
          )}
        </div>
        {isOverflowing && !isScrolledToBottom && (
          <div className="pointer-events-none sticky -bottom-4 -left-4 -right-4 -mt-12 h-12 bg-gradient-to-b from-transparent to-card"></div>
        )}
      </div>
    </ShineCard>
  );
};

export const EditThoughtTool: TOOL_RENDERER = {
  type: TOOL_TYPES.EDIT_THOUGHT,
  renderer: EditThoughtCard,
  needRefreshBoard: true,
  getToolTitle: (block) => {
    return <ToolStatus logo={<CreateThought size={16} />} commandName={getToolTitle(block)} />;
  },
};

/**
 * 移除图片链接中的特定后缀（@large, @medium, @small）
 */
export function removeImageSuffixes(text: string): string {
  // 匹配Markdown图片语法，正则捕获组分为前缀、链接和后缀
  const imageRegex = /(!?\[.*?\]\()(.*?)(\))/g;

  return text.replace(imageRegex, (_match, prefix, url, suffix) => {
    // 去除链接中的 @large, @medium, @small 后缀
    const cleanedUrl = url.replace(/@(large|medium|small)(\?.*?)?$/, '$2');
    return `${prefix}${cleanedUrl}${suffix}`;
  });
}
