{"name": "youweb", "version": "1.0.0", "private": true, "type": "module", "scripts": {"build:preview": "YOUMIND_ENV=preview NODE_ENV=production ../../devops/scripts/with-doppler rsbuild build", "build:production": "YOUMIND_ENV=production NODE_ENV=production ../../devops/scripts/with-doppler rsbuild build", "build:cf": "../../devops/scripts/with-doppler rsbuild build && cp public/_redirects dist/ && cp -r functions dist/", "dev:preview": "YOUMIND_ENV=preview NODE_ENV=development PROXY_TO=preview ../../devops/scripts/with-doppler rsbuild dev --open", "dev": "YOUMIND_ENV=preview NODE_ENV=development PROXY_TO=local ../../devops/scripts/with-doppler rsbuild dev", "dev:production": "YOUMIND_ENV=production NODE_ENV=development PROXY_TO=production ../../devops/scripts/with-doppler rsbuild dev", "format": "biome format . --write", "format:check": "biome format .", "lint": "biome check . --diagnostic-level=error", "lint:fix": "biome check . --write --diagnostic-level=error", "preview": "rsbuild preview", "env": "node ../../devops/scripts/doppler-pull.js youweb", "env:prod": "node ../../devops/scripts/doppler-pull.js youweb --env=production", "config": "node ../../devops/scripts/doppler-pull-config.js", "config:prod": "node ../../devops/scripts/doppler-pull-config.js --env=production", "typecheck": "tsc --noEmit"}, "dependencies": {"@dnd-kit/core": "^6.1.0", "@dnd-kit/sortable": "^8.0.0", "@dnd-kit/utilities": "^3.2.2", "@floating-ui/react": "^0.26.28", "@hookform/resolvers": "^3.9.1", "@infinite-canvas-tutorial/ecs": "0.0.1-alpha.36", "@infinite-canvas-tutorial/webcomponents": "0.0.1-alpha.36", "@nivo/core": "^0.99.0", "@nivo/line": "^0.99.0", "@opentelemetry/api": "^1.9.0", "@opentelemetry/core": "^2.0.1", "@opentelemetry/exporter-trace-otlp-http": "^0.203.0", "@opentelemetry/resources": "^2.0.1", "@opentelemetry/sdk-trace-base": "^2.0.1", "@opentelemetry/sdk-trace-web": "^2.0.1", "@opentelemetry/semantic-conventions": "^1.34.0", "@radix-ui/react-accordion": "^1.2.3", "@radix-ui/react-alert-dialog": "^1.1.2", "@radix-ui/react-avatar": "^1.1.1", "@radix-ui/react-checkbox": "^1.1.2", "@radix-ui/react-collapsible": "^1.1.1", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-dropdown-menu": "2.1.15", "@radix-ui/react-hover-card": "^1.1.2", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-menubar": "^1.1.2", "@radix-ui/react-popover": "^1.1.2", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-radio-group": "^1.2.1", "@radix-ui/react-scroll-area": "^1.2.1", "@radix-ui/react-select": "^2.1.2", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slider": "^1.2.1", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.1.1", "@radix-ui/react-tabs": "^1.1.1", "@radix-ui/react-toast": "^1.2.2", "@radix-ui/react-toggle": "^1.1.0", "@radix-ui/react-toggle-group": "^1.1.0", "@radix-ui/react-tooltip": "^1.1.4", "@radix-ui/react-use-controllable-state": "^1.1.0", "@repo/api": "workspace:*", "@repo/common": "workspace:*", "@repo/config": "workspace:*", "@repo/di": "workspace:*", "@repo/editor-common": "workspace:*", "@repo/ui": "workspace:*", "@repo/ui-business-editor": "workspace:*", "@repo/ui-business-snip": "workspace:*", "@sentry/react": "^9.38.0", "@supabase/ssr": "catalog:", "@supabase/supabase-js": "catalog:", "@tiptap/core": "catalog:", "@tiptap/extension-collaboration": "catalog:", "@tiptap/extension-dropcursor": "catalog:", "@tiptap/extension-file-handler": "catalog:", "@tiptap/extension-gapcursor": "catalog:", "@tiptap/extension-history": "catalog:", "@tiptap/extension-placeholder": "catalog:", "@tiptap/pm": "catalog:", "@tiptap/react": "catalog:", "@types/turndown": "^5.0.5", "@youmindinc/jsbridge": "catalog:", "@youmindinc/youcommon": "catalog:", "@youmindinc/youicon": "catalog:", "@youmindinc/youmind-translate": "1.0.1", "ahooks": "catalog:", "antd": "catalog:", "canvas-size": "^2.0.0", "class-variance-authority": "catalog:", "clsx": "catalog:", "cmdk": "^1.0.4", "copy-to-clipboard": "^3.3.3", "date-fns": "^3.6.0", "framer-motion": "catalog:", "franc-min": "catalog:", "highlight.js": "catalog:", "howler": "^2.2.4", "html-to-image": "^1.11.13", "i18next": "^25.3.2", "immer": "^10.0.3", "iso-639-3": "catalog:", "jotai": "catalog:", "jotai-scope": "^0.9.3", "jsonrepair": "^3.12.0", "katex": "^0.16.21", "lodash": "catalog:", "lodash-es": "catalog:", "lowlight": "catalog:", "lucide-react": "catalog:", "marked": "^13.0.3", "marked-highlight": "^2.2.1", "marked-katex-extension": "^5.1.4", "masonry-layout": "^4.2.2", "mermaid": "catalog:", "pdfjs-dist": "4.8.69", "posthog-js": "catalog:", "query-string": "^9.1.1", "react": "catalog:", "react-blurhash": "^0.3.0", "react-copy-to-clipboard": "^5.1.0", "react-day-picker": "^8.10.1", "react-dom": "catalog:", "react-dropzone": "^14.3.5", "react-error-boundary": "^6.0.0", "react-fast-marquee": "^1.6.5", "react-helmet": "^6.1.0", "react-hook-form": "catalog:", "react-i18next": "^15.6.0", "react-markdown": "^10.1.0", "react-masonry-component": "^6.3.0", "react-pdf": "9.2.1", "react-router": "^7.6.3", "react-router-dom": "catalog:", "react-syntax-highlighter": "^15.6.1", "react-tweet": "^3.2.1", "react-window": "^1.8.10", "react-youtube": "^10.1.0", "rehype-highlight": "^7.0.2", "rehype-katex": "^7.0.1", "rehype-raw": "^7.0.0", "remark-gfm": "^4.0.1", "remark-math": "^6.0.0", "sonner": "^2.0.6", "tailwind-merge": "catalog:", "tippy.js": "catalog:", "turndown": "^7.2.0", "unist-util-visit": "^5.0.0", "use-stick-to-bottom": "^1.1.1", "usehooks-ts": "^3.1.0", "uuidv7": "catalog:", "validator": "catalog:", "vaul": "^1.1.2", "y-indexeddb": "catalog:", "y-prosemirror": "catalog:", "yjs": "catalog:", "zod": "catalog:"}, "devDependencies": {"@cloudflare/workers-types": "catalog:", "@rsbuild/plugin-node-polyfill": "^1.3.2", "@rsbuild/plugin-sass": "^1.3.3", "@types/canvas-size": "^1.2.2", "@types/howler": "catalog:", "@types/lodash": "catalog:", "@types/lodash-es": "catalog:", "@types/masonry-layout": "^4.2.8", "@types/react": "catalog:", "@types/react-copy-to-clipboard": "^5.0.7", "@types/react-dom": "catalog:", "@types/react-helmet": "^6.1.11", "@types/react-syntax-highlighter": "^15.5.13", "@types/react-window": "^1.8.8", "@types/validator": "catalog:", "autoprefixer": "^10.4.21", "rsbuild-plugin-html-minifier-terser": "^1.1.1", "tailwindcss": "^3.4.17"}}