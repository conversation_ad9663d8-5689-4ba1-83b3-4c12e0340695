{"name": "<PERSON>admin", "version": "0.1.0", "private": true, "scripts": {"dev": "YOUMIND_ENV=preview ../../devops/scripts/with-doppler next dev -p 3002", "dev:cf": "YOUMIND_ENV=preview CLOUDFLARE_ACCOUNT_ID=ce1ed653c688afb19ad455cde3ba278a ../../devops/scripts/with-doppler next dev -p 3002", "dev:prod": "YOUMIND_ENV=production ../../devops/scripts/with-doppler next dev --port 3002", "build": "../../devops/scripts/with-doppler next build", "build:cf": "../../devops/scripts/with-doppler npx @opennextjs/cloudflare build", "start": "YOUMIND_ENV=production ../../devops/scripts/with-doppler next start", "preview": "wrangler dev", "deploy": "wrangler deploy", "lint": "biome check . --diagnostic-level=error", "lint:fix": "biome check . --write --diagnostic-level=error", "format": "biome format . --write", "format:check": "biome format .", "typecheck": "tsc --noEmit", "env": "node ../../devops/scripts/doppler-pull.js youadmin", "env:prod": "node ../../devops/scripts/doppler-pull.js youadmin --env=production", "config": "node ../../devops/scripts/doppler-pull-config.js", "config:prod": "node ../../devops/scripts/doppler-pull-config.js --env=production"}, "dependencies": {"@ai-sdk/openai": "^1.3.22", "@clickhouse/client-web": "^1.11.2", "@google-analytics/data": "^4.12.1", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.1.12", "@repo/config": "workspace:*", "@repo/server-common": "workspace:*", "@tanstack/react-table": "^8.21.3", "ai": "^4.3.16", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "lucide-react": "^0.468.0", "next": "catalog:", "react": "catalog:", "react-dom": "catalog:", "react-markdown": "^10.1.0", "recharts": "^3.0.2", "remark-gfm": "^4.0.1", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "zod": "^3.24.1"}, "devDependencies": {"@opennextjs/cloudflare": "^1.3.0", "@types/node": "catalog:", "@types/react": "catalog:", "@types/react-dom": "catalog:", "autoprefixer": "^10.4.20", "postcss": "^8.5.1", "tailwindcss": "^3.4.17", "typescript": "catalog:", "wrangler": "catalog:"}}