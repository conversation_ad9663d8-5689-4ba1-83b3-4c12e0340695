/**
 * @doc https://docs.nestjs.com/recipes/async-local-storage
 * Continuation Local Storage 服务
 * 提供请求级别的上下文存储，用于存储 OTEL 相关的值（traceId/userId/spaceId）
 * 替代在单例服务中传递 request 对象的模式
 */

import { Injectable } from '@nestjs/common';
import { ClsService } from 'nestjs-cls';

import type { SpaceDO, UserDO } from '@/dao';

import { OTELKeyEnum } from '../tracing';

/**
 * CLS 存储的接口定义
 * 包含请求生命周期中需要访问的关键信息
 */
export interface ClsStore extends Record<string | symbol, any> {
  [OTELKeyEnum.TRACE_ID]?: string;
  [OTELKeyEnum.USER_ID]?: string;
  [OTELKeyEnum.SPACE_ID]?: string;
  transaction?: any; // 数据库事务对象
  accessToken?: string; // 缓存的访问令牌，避免重复调用 getSession
}

@Injectable()
export class YouapiClsService {
  constructor(private readonly cls: ClsService<ClsStore>) {}

  /**
   * 获取当前请求的 traceId
   */
  getTraceId(): string | undefined {
    return this.cls.get('traceId');
  }

  /**
   * 设置当前请求的 traceId
   */
  setTraceId(traceId: string): void {
    this.cls.set('traceId', traceId);
  }

  /**
   * 获取当前请求的 userId
   */
  getUserId(): string | undefined {
    return this.cls.get('userId');
  }

  /**
   * 获取当前请求的 user.name
   */
  getUserName(): string | undefined {
    return this.cls.get('user')?.name;
  }

  /**
   * 设置当前请求的 userId
   */
  setUserId(userId: string): void {
    this.cls.set('userId', userId);
  }

  /**
   * 设置当前请求的 user
   */
  setUser(user: UserDO): void {
    this.cls.set('user', user);
    this.cls.set('userId', user.id);
  }

  /**
   * 获取当前请求的 spaceId
   */
  getSpaceId(): string | undefined {
    return this.cls.get('spaceId');
  }

  /**
   * 设置当前请求的 spaceId
   */
  setSpaceId(spaceId: string): void {
    this.cls.set('spaceId', spaceId);
  }

  /**
   * 获取当前请求的 space
   */
  getSpace(): SpaceDO | undefined {
    return this.cls.get('space');
  }

  /**
   * 设置当前请求的 space
   */
  setSpace(space: SpaceDO): void {
    this.cls.set('space', space);
    this.cls.set('spaceId', space.id);
  }

  /**
   * 获取当前事务
   */
  getTransaction(): any {
    return this.cls.get('transaction');
  }

  /**
   * 设置当前事务
   */
  setTransaction(transaction: any): void {
    this.cls.set('transaction', transaction);
  }

  /**
   * 清除当前事务
   */
  clearTransaction(): void {
    this.cls.set('transaction', undefined);
  }

  /**
   * 获取缓存的访问令牌
   */
  getAccessToken(): string | undefined {
    return this.cls.get('accessToken');
  }

  /**
   * 设置访问令牌到缓存
   */
  setAccessToken(token: string): void {
    this.cls.set('accessToken', token);
  }

  /**
   * 获取当前请求的 messageId
   */
  getMessageId(): string | undefined {
    return this.cls.get('messageId');
  }

  /**
   * 设置当前 MessageId
   */
  setMessageId(messageId: string): void {
    this.cls.set('messageId', messageId);
  }
}
