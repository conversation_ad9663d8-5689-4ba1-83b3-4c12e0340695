export function camelToSnakeKey(key: string): string {
  // 处理特殊情况: ai2ndResponseLanguage -> ai_2nd_response_language
  if (key === 'ai2ndResponseLanguage') {
    return 'ai_2nd_response_language';
  }

  // 通用的 camelCase 到 snake_case 转换
  return key.replace(/([a-z])([A-Z])/g, '$1_$2').toLowerCase();
}

export function camelToSnake<T = any>(value: T, blackListSet?: Set<string>) {
  const BLACK_LIST_SET = blackListSet ?? new Set();
  if (value === null || value === undefined) {
    return value;
  }
  if (Array.isArray(value)) {
    return value.map((v) => camelToSnake(v, BLACK_LIST_SET));
  }

  if (typeof value === 'object' && !(value instanceof Date)) {
    return Object.fromEntries(
      Object.entries(value).map(([key, value]) => [
        BLACK_LIST_SET.has(key) ? key : camelT<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(key),
        camelToSnake(value),
      ]),
    );
  }
  return value;
}

export function snakeToCamel<T = any>(value: T) {
  if (value === null || value === undefined) {
    return value;
  }

  if (Array.isArray(value)) {
    return value.map(snakeToCamel);
  }

  const impl = (str: string) => {
    const converted = str.replace(/([-_]\w)/g, (group) => group[1].toUpperCase());
    return converted[0].toLowerCase() + converted.slice(1);
  };

  if (typeof value === 'object' && !(value instanceof Date)) {
    return Object.fromEntries(
      Object.entries(value).map(([key, value]) => [impl(key), snakeToCamel(value)]),
    );
  }
  return value;
}
