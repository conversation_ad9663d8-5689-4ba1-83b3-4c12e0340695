import { Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { encode } from 'blurhash';
import { omit } from 'lodash';
import OpenAI from 'openai';
import {
  type ImageEditParams,
  type ImageGenerateParams,
  ImagesResponse,
} from 'openai/resources/images';
import { toFile } from 'openai/uploads';
import sharp from 'sharp';
import { LangfuseGeneration, LangfuseSpan } from '@/common/decorators';
import { LLMs } from '@/common/types';
import { imageUrlToBytesAndFormat } from '@/common/utils';
import { ApplicationContext } from '@/common/utils/application-context';
import { FileDomainService } from '@/domain/file';
import { Directory } from '@/domain/file/types';
import { YLImageEditOptions } from '@/infra/youllm';
import { Generation } from '../domain/models/generation.entity';
import { PromptService } from '../prompt/index.service';
import { BaseRunner } from './base';
import { TextRunner } from './text';

interface EditImageMask {
  x: number;
  y: number;
  width: number;
  height: number;
}

/**
 * ImageRunner - migrated from youapp image functions
 * Provides editImage and imageResponseToResult methods matching youapp exactly
 */
export class ImageRunner extends BaseRunner {
  protected readonly logger = new Logger(ImageRunner.name);

  private fileDomainService: FileDomainService;
  private configService: ConfigService;
  private openaiClient: OpenAI;

  constructor() {
    super();
    this.fileDomainService = ApplicationContext.getProvider<FileDomainService>(FileDomainService);
    this.configService = ApplicationContext.getProvider<ConfigService>(ConfigService);
    this.openaiClient = new OpenAI({
      apiKey: this.configService.get('OPENAI_API_KEY'),
    });
  }

  /**
   * Edit an existing image - exact migration from youapp editImage function
   * @param user_id - User ID
   * @param url - Image URL to edit
   * @param prompt - Edit prompt
   * @param size - Image size
   * @param mask - JSON string of mask regions
   * @param quality - Image quality
   */
  @LangfuseGeneration({
    name: 'image-edit',
    model: LLMs.GPT_IMAGE_1,
  })
  async editImage(
    url: string,
    prompt: string,
    size: string,
    mask: string,
    quality: 'low' | 'medium' | 'high' | 'auto',
  ) {
    const masks = JSON.parse(mask) as EditImageMask[];
    // const space = await this.spaceDomainService.getByUserId(user_id);

    // 获取原图像的尺寸信息
    const { bytes: imageBytes } = await imageUrlToBytesAndFormat(url);
    const originalImage = sharp(imageBytes);
    const { width: originalWidth, height: originalHeight } = await originalImage.metadata();

    if (!originalWidth || !originalHeight) {
      throw new Error('Could not determine original image dimensions');
    }

    // 获取原图像的 RGBA 原始数据
    const { data: originalImageData } = await originalImage
      .ensureAlpha()
      .raw()
      .toBuffer({ resolveWithObject: true });

    // 创建一个与原图像相同尺寸的 mask，初始时完全复制原图像
    // mask 图像中，完全透明的区域（alpha = 0）表示要编辑的区域
    // Mask is an additional image whose fully transparent areas (e.g. where alpha is zero) indicate where image should be edited.
    const maskBuffer = Buffer.from(originalImageData);

    // 根据 masks 数组将指定区域设为透明（要编辑的区域）
    masks.forEach(({ x, y, width, height }) => {
      for (let row = y; row < Math.min(y + height, originalHeight); row++) {
        for (let col = x; col < Math.min(x + width, originalWidth); col++) {
          const index = (row * originalWidth + col) * 4;
          if (index + 3 < maskBuffer.length) {
            maskBuffer[index + 3] = 0; // 设为完全透明
          }
        }
      }
    });

    const maskImageBuffer = await sharp(maskBuffer, {
      raw: {
        width: originalWidth,
        height: originalHeight,
        channels: 4,
      },
    })
      .png()
      .toBuffer();

    // 将 mask 转换为 Uploadable 格式
    const maskUploadable = await toFile(maskImageBuffer, null, {
      type: 'image/png',
    });

    const params: Partial<YLImageEditOptions> = {
      size: size as YLImageEditOptions['size'],
      n: 1,
      quality: quality || 'medium',
      mask: maskUploadable,
    };

    const { bytes, format } = await imageUrlToBytesAndFormat(url);
    const image = await toFile(bytes, null, {
      type: format,
    });

    const imageResponse = await this.callEditImage({
      prompt: `${prompt} Use the following style: ${params.style}`,
      image: image,
      ...omit(params, ['style']),
    });

    // // Record usage in background (using runInBackground equivalent)
    // this.usageRecordDomainService
    //   .createImageGenerationUsageRecord({
    //     space_id: space.id,
    //     user_id,
    //     amount: imageResponse.usage?.total_tokens || 0,
    //   })
    //   .catch((error) => {
    //     this.logger.error('Failed to record image edit usage', error);
    //   });
    const { hash, image_url, blurhash, width, height } =
      await this.imageResponseToResult(imageResponse);

    await this.traceService.updateGeneration({
      output: {
        image: image_url,
      },
      usage: {
        input: imageResponse?.usage?.input_tokens || 0,
        output: imageResponse?.usage?.output_tokens || 0,
        total: imageResponse?.usage?.total_tokens || 0,
      },
    });

    return {
      hash,
      imageUrl: image_url,
      originalImageUrl: url,
      blurhash,
      width,
      height,
      quality,
    };
  }

  @LangfuseSpan({
    name: 'call-api-openai',
  })
  private async callEditImage(param: ImageEditParams) {
    return await this.openaiClient.images.edit(param);
  }

  /**
   * Generate image using DALL-E through tool (for chat integration)
   * This matches the tool_function from youapp image_generate.ts
   */
  @LangfuseGeneration({
    name: 'image-generate',
    model: LLMs.GPT_IMAGE_1,
  })
  async generateImage(params: {
    // @see https://platform.openai.com/docs/guides/image-generation#customize-image-output
    size: '1024x1024' | '1536x1024' | '1024x1536' | 'auto';
    quality: 'low' | 'medium' | 'high' | 'auto';
    // @see https://platform.openai.com/docs/guides/image-generation?image-generation-model=gpt-image-1&lang=javascript#transparency
    background: 'transparent' | 'opaque' | 'auto';
    n: number;
    prompt: string;
  }) {
    // const space = await this.spaceDomainService.getByUserId(userId);
    // await this.usageRecordDomainService.checkQuota(space, QuotaResourceEnum.IMAGE_GENERATION);
    const imageResponse = await this.callGenerateImage(params);

    const { hash, image_url, blurhash, width, height } =
      await this.imageResponseToResult(imageResponse);

    // // Record usage in background
    // this.usageRecordDomainService
    //   .createImageGenerationUsageRecord({
    //     space_id: space.id,
    //     user_id: userId,
    //     amount: imageResponse.usage?.total_tokens || 0,
    //   })
    //   .catch((error) => {
    //     this.logger.error('Failed to record image generation usage', error);
    //   });

    await this.traceService.updateGeneration({
      output: {
        image: image_url,
      },
      usage: {
        input: imageResponse?.usage?.input_tokens || 0,
        output: imageResponse?.usage?.output_tokens || 0,
        total: imageResponse?.usage?.total_tokens || 0,
      },
    });
    return {
      hash,
      imageUrl: image_url,
      blurhash,
      width,
      height,
    };
  }

  @LangfuseSpan({
    name: 'call-api-openai',
  })
  private async callGenerateImage(param: ImageGenerateParams) {
    return await this.openaiClient.images.generate(param);
  }

  /**
   * Convert image response to result with file upload and blurhash
   * Exact migration from youapp imageResponseToResult function
   */
  async imageResponseToResult(imageResponse: ImagesResponse, blurhashEnabled = true) {
    const image_bytes = Buffer.from(imageResponse.data?.[0].b64_json || '', 'base64');

    const hash = await this.fileDomainService.uploadStringOrBuffer(image_bytes, {
      visibility: 'public',
      contentType: 'image/png',
      directory: Directory.GEN_IMAGES,
    });

    const image_url = `https://cdn.gooo.ai/${Directory.GEN_IMAGES}/${hash}.png`;

    if (!blurhashEnabled) {
      return {
        hash,
        image_url,
      };
    }

    const image = sharp(image_bytes);
    const metadata = await image.metadata();

    if (!metadata.width || !metadata.height) {
      throw new Error('Invalid image: Could not determine dimensions');
    }

    const { data, info } = await image.ensureAlpha().raw().toBuffer({ resolveWithObject: true });

    const blurhash = encode(new Uint8ClampedArray(data), info.width, info.height, 4, 4);

    return {
      hash,
      image_url,
      blurhash,
      width: metadata.width,
      height: metadata.height,
    };
  }

  @LangfuseGeneration({
    name: 'generate-svg-diagram',
    model: LLMs.CLAUDE_4_SONNET,
  })
  async generateDiagram(param: { text: string; type: string; size: string }) {
    const { text, type, size } = param;

    const promptService = ApplicationContext.getProvider<PromptService>(PromptService);
    const { prompt, promptMessages } = promptService.getPromptAndMessages('svg-generator-prompt', {
      text,
      type,
      size,
    });
    const generation = new Generation({
      prompt,
      promptMessages,
      model: LLMs.CLAUDE_4_SONNET,
    });
    const runner = new TextRunner();
    runner.addGeneration(generation);
    return await runner.generateOnce();
  }

  @LangfuseGeneration({
    name: 'optimize-svg',
    model: LLMs.GEMINI_25_PRO,
  })
  async optimizeSVG(param: { svg: string; type: string }) {
    const { svg, type } = param;

    const promptService = ApplicationContext.getProvider<PromptService>(PromptService);
    const { prompt, promptMessages } = promptService.getPromptAndMessages('svg-optimizer-prompt', {
      svg,
      type,
    });
    const generation = new Generation({
      prompt,
      promptMessages,
      model: LLMs.GEMINI_25_PRO,
    });
    const runner = new TextRunner();
    runner.addGeneration(generation);
    return runner.generateStream();
  }

  /**
   * Upload SVG content and return URL - for diagram generation workflow
   */
  async uploadSVG(svgContent: string): Promise<{ hash: string; svg_url: string }> {
    const formattedSVG = this.formatSVG(svgContent);

    const hash = await this.fileDomainService.uploadStringOrBuffer(Buffer.from(formattedSVG), {
      visibility: 'public',
      contentType: 'image/svg+xml',
      directory: Directory.GEN_IMAGES,
    });

    const svg_url = `https://cdn.gooo.ai/${Directory.GEN_IMAGES}/${hash}.svg`;

    return {
      hash,
      svg_url,
    };
  }

  /**
   * Format SVG content - escape & to &amp; to avoid xmlParseEntityRef errors
   */
  public formatSVG(svg: string): string {
    return (svg || '')
      .replace(/&/g, '&amp;')
      .replace('```xml', '') // remove ```xml and ```
      .replace('```svg', '') // remove ```svg and ```
      .replace('```other', '') // remove ```other and ```
      .replace('```', '');
  }
}
