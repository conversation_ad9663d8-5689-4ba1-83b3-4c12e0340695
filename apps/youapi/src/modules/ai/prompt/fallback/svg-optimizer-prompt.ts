/**
 *  THIS FILE IS AUTO GENERATED, DO NOT MODIFY
 *  Generated using `npm run prompt`
 **/
export const prompt = {
  prompt: [
    {
      role: 'user',
      content: `请优化下面的 SVG 并返回最终的产物，它的类型是 {{type}}。在整体布局上需要避免大量留白，避免文本重叠和超出容器的情况，但不要使用 CSS 类，也不要使用 <tspan> 元素去尝试拆分 <text> 元素，也不要使用 额外的 <g> 元素尝试给图形编组。
SVG 内容如下：{{svg}}

请不要返回除了最终 SVG 产物之外的任何文字，例如你的优化思路。`,
    },
  ],
  config: {},
  name: 'svg-optimizer-prompt',
  version: 4,
};
