import { QueryBus } from '@nestjs/cqrs';
import { CoreMessage } from 'ai';
import { ChatCompletionToolChoiceOption } from 'openai/resources/index.js';
import { ChatOriginTypeEnum, EditCommandTypeEnum, ToolNames } from '@/common/types';
import { ApplicationContext } from '@/common/utils/application-context';
import { Generation } from '@/modules/ai/domain/models/generation.entity';
import {
  BoardDirectoryStructureDto,
  BoardItemType,
  GetBoardDirectoryStructureQuery,
} from '@/modules/material-mng';
import { Chat } from '../../domain/chat/models/chat.entity';
import { SendMessageCommand } from '../commands/send-message.command';
import { ChatRunner } from './chat';

export class AgentRunner extends ChatRunner {
  protected readonly traceName = 'chat-assistant-agent';
  protected readonly prompt = 'writer-system-prompt';
  protected readonly queryBus: QueryBus;
  private boardName: string;
  private boardDirectoryStructure: BoardDirectoryStructureDto['directoryStructure'];

  constructor(chat: Chat, userId: string) {
    super(chat, userId);
    this.queryBus = ApplicationContext.getProvider<QueryBus>(QueryBus);
  }

  protected async getPromptVariables(): Promise<Record<string, any>> {
    const aiLanguage = await this.userDomainService.getPrimaryResponseLanguage({
      user_id: this.userId,
    });
    const boardId = this.chat.getBoardId();
    const spaceId = this.youapiClsService.getSpaceId();
    if (boardId) {
      const { directoryStructure, name } = await this.queryBus.execute(
        new GetBoardDirectoryStructureQuery(this.userId, spaceId, boardId),
      );
      this.boardName = name;
      this.boardDirectoryStructure = directoryStructure;
    }

    const assistantMessage = this.chat.getLastAssistantMessage();
    const user = await this.userDomainService.selectOneById(this.userId);
    return {
      model: assistantMessage.model,
      userName: user.name,
      language: aiLanguage,
      currentTime: new Date().toLocaleString(),
      boardName: this.boardName || 'Unsorted',
      boardDirectoryStructure: this.boardDirectoryStructure
        ? JSON.stringify(this.boardDirectoryStructure, null, 2)
        : 'No directory structure',
    };
  }

  protected async preparePromptMessages(): Promise<CoreMessage[]> {
    // 把上一轮的消息返回
    if (this.currentGeneration) {
      return [
        ...this.currentGeneration.promptMessages,
        ...this.currentGeneration.generatedMessages,
      ];
    }

    // 首次生成消息
    const { promptMessages } = await this.promptService.getPromptAndMessages(
      this.prompt,
      await this.getPromptVariables(),
    );
    return [...promptMessages, ...this.chat.toCoreMessages()];
  }

  protected getToolSetup(command: SendMessageCommand): {
    tools: ToolNames[];
    toolChoice: ChatCompletionToolChoiceOption;
  } {
    let tools: ToolNames[] = [
      ToolNames.AUDIO_GENERATE,
      ToolNames.IMAGE_GENERATE,
      ToolNames.DIAGRAM_GENERATE,
      ToolNames.GOOGLE_SEARCH,
      ToolNames.LIBRARY_SEARCH,
      ToolNames.BOARD_SEARCH,
      ToolNames.EDIT_THOUGHT,
      ToolNames.CREATE_SNIP_BY_URL,
    ];
    let toolChoice: ChatCompletionToolChoiceOption = 'auto';

    if (this.chat.origin?.type === ChatOriginTypeEnum.WEBPAGE) {
      tools = tools.filter((tool) =>
        [ToolNames.LIBRARY_SEARCH, ToolNames.BOARD_SEARCH, ToolNames.CREATE_SNIP_BY_URL].includes(
          tool,
        ),
      );
    }
    if (this.boardDirectoryStructure) {
      // 如果 board 里没有可搜索的 snip 或 thought，则不挂 board_search 工具
      const hasSearchableContent = (items: BoardDirectoryStructureDto['directoryStructure']) => {
        return items.some((item) => {
          if (item.entityType === BoardItemType.THOUGHT || item.entityType === BoardItemType.SNIP) {
            return true;
          }
          if (item.entityType === BoardItemType.BOARD_GROUP && item.children) {
            return hasSearchableContent(
              item.children as BoardDirectoryStructureDto['directoryStructure'],
            );
          }
          return false;
        });
      };
      const isEmptyBoard = !hasSearchableContent(this.boardDirectoryStructure);
      if (isEmptyBoard) {
        tools = tools.filter((tool) => tool !== ToolNames.BOARD_SEARCH);
      }
    }

    const hasToolHistory = this.getGeneratedCoreMessages().some(
      (message) => message.role === 'tool',
    );
    if (hasToolHistory) {
      return {
        tools,
        toolChoice,
      };
    }

    // 通过 tool 数组强制使用工具
    const requiredToolUse = Object.keys(command.param.tools).find(
      (tool) => command.param.tools?.[tool]?.useTool === 'required',
    );
    if (requiredToolUse && tools.includes(requiredToolUse as ToolNames)) {
      toolChoice = {
        type: 'function',
        function: { name: requiredToolUse },
      };
      return {
        tools,
        toolChoice,
      };
    }

    // 通过 command 对象强制使用工具
    let requiredToolUseByCommand;
    switch (command.param.command?.type) {
      case EditCommandTypeEnum.SUGGEST_SEARCH:
        requiredToolUseByCommand = ToolNames.GOOGLE_SEARCH;
        break;
      case EditCommandTypeEnum.SUGGEST_GENERATE_TEXT:
        requiredToolUseByCommand = ToolNames.EDIT_THOUGHT;
        break;
      case EditCommandTypeEnum.SUGGEST_GENERATE_AUDIO:
        requiredToolUseByCommand = ToolNames.AUDIO_GENERATE;
        break;
      case EditCommandTypeEnum.SUGGEST_GENERATE_IMAGE:
        requiredToolUseByCommand = ToolNames.IMAGE_GENERATE;
        break;
      default:
        break;
    }
    if (requiredToolUseByCommand && tools.includes(requiredToolUseByCommand as ToolNames)) {
      toolChoice = {
        type: 'function',
        function: { name: requiredToolUseByCommand },
      };
      return {
        tools,
        toolChoice,
      };
    }

    return {
      tools,
      toolChoice,
    };
  }

  protected async setupGeneration(command: SendMessageCommand) {
    const assistantMessage = this.chat.getLastAssistantMessage();
    const promptMessages = await this.preparePromptMessages();

    const generation = new Generation({
      ...this.getToolSetup(command),
      model: assistantMessage.model,
      prompt: this.promptService.fetchPrompt({ name: this.prompt }),
      promptMessages,
      traceMetadata: this.chat.getTraceMetadata(),
    });
    this.generations.push(generation);
    this.currentGeneration = generation;
    this.currentGeneration.setChatMessageId(assistantMessage.id).setBizArgs({
      chatId: this.chat.id,
      userId: this.userId,
      messageId: assistantMessage.id,
    });
  }
}
