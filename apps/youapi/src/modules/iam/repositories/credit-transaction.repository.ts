import { Injectable, NotFoundException } from '@nestjs/common';
import { and, desc, eq, gte, lte } from 'drizzle-orm';
import { BaseRepository } from '@/common/database/base.repository';
import { creditTransactions } from '../../../shared/db/public.schema';
import { CreditTransaction } from '../domain/credit/models/credit-transaction.entity';
import { CreditType, TransactionType } from '../domain/credit/models/credit-transaction.types';

// 数据库对象类型
type CreditTransactionDO = typeof creditTransactions.$inferSelect;

// 查询参数接口
export interface FindTransactionsParams {
  spaceId?: string;
  creditAccountId?: string;
  type?: TransactionType;
  startDate?: Date;
  endDate?: Date;
  limit?: number;
  offset?: number;
}

// 当前周期查询参数接口
export interface FindConsumeTransactionsInCurrentPeriodParams {
  creditAccountId: string;
  currentPeriodStart: Date;
  currentPeriodEnd: Date;
}

/**
 * 积分交易 Repository
 *
 * 管理积分交易记录的持久化和查询操作
 * 支持多维度查询和分页
 */
@Injectable()
export class CreditTransactionRepository extends BaseRepository {
  /**
   * 按 ID 查找积分交易
   */
  async findById(id: string): Promise<CreditTransaction | null> {
    const result = await this.db
      .select()
      .from(creditTransactions)
      .where(eq(creditTransactions.id, id));

    if (result.length === 0) {
      return null;
    }

    return this.doToEntity(result[0]);
  }

  /**
   * 按 ID 获取积分交易 - 必须存在
   */
  async getById(id: string): Promise<CreditTransaction> {
    const transaction = await this.findById(id);
    if (!transaction) {
      throw new NotFoundException(`Credit transaction ${id} not found`);
    }
    return transaction;
  }

  /**
   * 保存积分交易
   */
  async save(transaction: CreditTransaction): Promise<void> {
    const do_ = this.entityToDO(transaction);

    if (transaction.isNew) {
      await this.db.insert(creditTransactions).values(do_);
      transaction.markAsExisting();
    }
    // 注意：交易记录一般不允许修改，只插入
  }

  /**
   * 批量保存积分交易
   */
  async createMany(transactions: CreditTransaction[]): Promise<void> {
    if (transactions.length === 0) return;

    const doList = transactions.filter((t) => t.isNew).map((t) => this.entityToDO(t));

    if (doList.length > 0) {
      await this.db.insert(creditTransactions).values(doList);
      transactions.forEach((t) => t.markAsExisting());
    }
  }

  /**
   * 按条件查找积分交易
   */
  async findByParams(params: FindTransactionsParams): Promise<CreditTransaction[]> {
    let query = this.db.select().from(creditTransactions).$dynamic();

    // 构建查询条件
    const conditions = [];

    if (params.spaceId) {
      conditions.push(eq(creditTransactions.spaceId, params.spaceId));
    }

    if (params.creditAccountId) {
      conditions.push(eq(creditTransactions.creditAccountId, params.creditAccountId));
    }

    // userId field removed from schema per design document

    if (params.type) {
      conditions.push(eq(creditTransactions.type, params.type));
    }

    if (params.startDate) {
      conditions.push(gte(creditTransactions.createdAt, params.startDate));
    }

    if (params.endDate) {
      conditions.push(lte(creditTransactions.createdAt, params.endDate));
    }

    if (conditions.length > 0) {
      query = query.where(and(...conditions));
    }

    // 按创建时间降序排列
    query = query.orderBy(desc(creditTransactions.createdAt));

    // 分页
    if (params.limit) {
      query = query.limit(params.limit);
    }

    if (params.offset) {
      query = query.offset(params.offset);
    }

    const result = await query;
    return result.map((row) => this.doToEntity(row));
  }

  /**
   * 按积分账户ID查找交易记录
   */
  async findByAccountId(accountId: string, limit = 50): Promise<CreditTransaction[]> {
    return this.findByParams({
      creditAccountId: accountId,
      limit,
    });
  }

  /**
   * 按空间ID查找交易记录
   */
  async findBySpaceId(spaceId: string, limit = 50): Promise<CreditTransaction[]> {
    return this.findByParams({
      spaceId,
      limit,
    });
  }

  /**
   * 查询当前周期的积分交易记录（按设计文档要求）
   */
  async findConsumeTransactionsInCurrentPeriodTransactions(
    params: FindConsumeTransactionsInCurrentPeriodParams,
  ): Promise<CreditTransaction[]> {
    const { creditAccountId, currentPeriodStart, currentPeriodEnd } = params;

    return this.findByParams({
      creditAccountId,
      startDate: currentPeriodStart,
      endDate: currentPeriodEnd,
      type: TransactionType.CONSUME,
    });
  }

  // ========== 实体转换方法 ==========

  /**
   * 实体转换为数据对象
   */
  private entityToDO(transaction: CreditTransaction): CreditTransactionDO {
    return {
      id: transaction.id,
      createdAt: transaction.createdAt,
      creditAccountId: transaction.creditAccountId,
      spaceId: transaction.spaceId,
      // userId field removed from schema
      type: transaction.type,
      creditType: transaction.creditType,
      amount: transaction.amount,
      balanceBefore: transaction.balanceBefore,
      balanceAfter: transaction.balanceAfter,
      reason: transaction.reason,
      metadata: transaction.metadata,
    };
  }

  /**
   * 数据对象转换为实体
   */
  private doToEntity(transactionDO: CreditTransactionDO): CreditTransaction {
    const transaction = new CreditTransaction({
      id: transactionDO.id,
      creditAccountId: transactionDO.creditAccountId,
      spaceId: transactionDO.spaceId,
      // userId field removed from schema
      type: transactionDO.type as TransactionType,
      creditType: transactionDO.creditType as CreditType,
      amount: transactionDO.amount,
      balanceBefore: transactionDO.balanceBefore,
      balanceAfter: transactionDO.balanceAfter,
      reason: transactionDO.reason,
      metadata: transactionDO.metadata as object,
      createdAt: transactionDO.createdAt,
    });

    transaction.markAsExisting();
    return transaction;
  }
}
