import { LanguageModelUsage } from 'ai';
import { LLMs } from '@/common/types';
import type { ProviderMetadata } from '../models/credit-account.types';
import type { LanguageModelTransactionCategory } from '../models/credit-transaction.types';

export class VercelAiUsageEvent {
  constructor(
    public readonly model: LLMs,
    public readonly usage: LanguageModelUsage,
    public readonly providerMetadata: ProviderMetadata,
    public readonly category: LanguageModelTransactionCategory,
  ) {}
}
