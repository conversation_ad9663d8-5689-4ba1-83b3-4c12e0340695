import { LLMProviders, LLMs } from '@/common/types';

// 交易类型枚举
export const TransactionType = {
  GRANT: 'grant',
  CONSUME: 'consume',
  FORFEIT: 'forfeit',
} as const;
export type TransactionType = (typeof TransactionType)[keyof typeof TransactionType];

// 积分类型枚举
export const CreditType = {
  MONTHLY: 'monthly',
} as const;
export type CreditType = (typeof CreditType)[keyof typeof CreditType];

// AI 使用类别枚举
export const LanguageModelTransactionCategory = {
  CHAT: 'chat',
  WRITING: 'writing',
  SEARCH: 'search',
  IMAGE: 'image',
  AUDIO: 'audio',
} as const;
export type LanguageModelTransactionCategory =
  (typeof LanguageModelTransactionCategory)[keyof typeof LanguageModelTransactionCategory];

export interface InputTokensDetail {
  text: number; // 基础文本输入 tokens
  image?: number; // 图像输入 tokens
  audio?: number; // 音频输入 tokens
  video?: number; // 视频输入 tokens

  // 缓存
  cacheHit?: number; // 缓存命中 tokens（大多数模型都有）
  // Claude 特有的细分缓存写入
  cacheWrite5m?: number; // 5分钟缓存写入 tokens
  cacheWrite1h?: number; // 1小时缓存写入 tokens
}
export interface OutputTokensDetail {
  text: number; // 基础文本输出 tokens
  image?: number; // 图像生成 tokens
  audio?: number; // 音频生成 tokens
  video?: number; // 视频生成 tokens（预留）

  // 推理
  reasoning?: number;
}

type InputCreditsDetail = InputTokensDetail;
type OutputCreditsDetail = OutputTokensDetail;

export interface TokenUsage {
  inputTokens: number;
  outputTokens: number;
  inputTokensDetail: InputTokensDetail;
  outputTokensDetail: OutputTokensDetail;
}

// 输入价格详细结构（第二层）
export interface InputPriceDetail {
  text: number; // 基础文本输入价格
  image?: number; // 图像输入价格
  audio?: number; // 音频输入价格
  video?: number; // 视频输入价格（预留）

  // 缓存
  cacheHit?: number; // 缓存命中价格（读取）
  // Claude 特有的缓存类型
  cacheWrite5m?: number; // 5分钟缓存写入价格
  cacheWrite1h?: number; // 1小时缓存写入价格
}

// 输出价格详细结构（第二层）
export interface OutputPriceDetail {
  text: number; // 基础文本输出价格
  image?: number; // 图像生成价格
  audio?: number; // 音频生成价格
  video?: number; // 视频生成价格

  // 推理
  reasoning?: number; // 推理价格
}

// 基础积分分解接口 - 增强版，支持多模态和细分计费
export interface BaseCreditBreakdown {
  totalCredits: number;
  inputCredits: number;
  outputCredits: number;

  inputCreditsDetail: InputCreditsDetail;
  outputCreditsDetail: OutputCreditsDetail;
}

export interface LanguageModelTransactionMetadata {
  provider: LLMProviders; // AI 提供商，使用枚举
  model: LLMs; // 模型标识，使用枚举
  category: LanguageModelTransactionCategory; // 必填：使用类别

  // 积分
  totalCredits: number;
  inputCredits: number;
  outputCredits: number;

  inputCreditsDetail: InputCreditsDetail;
  outputCreditsDetail: OutputCreditsDetail;

  // token usage
  tokenUsage: TokenUsage;

  // price breakdown
  priceBreakdown: {
    inputPrice: InputPriceDetail;
    outputPrice: OutputPriceDetail;
  };
}

export type CreditTransactionMetadata = LanguageModelTransactionMetadata | Record<string, any>;
