import { AggregateRoot } from '@nestjs/cqrs';
import { uuidv7 } from 'uuidv7';
import { TransactionRecordedEvent } from '../events/transaction-recorded.event';
import {
  CreditTransactionMetadata,
  CreditType,
  LanguageModelTransactionCategory,
  TransactionType,
} from './credit-transaction.types';

export interface CreditTransactionConstructorParams {
  id: string;
  creditAccountId: string;
  spaceId: string;
  type: TransactionType;
  creditType: CreditType;
  amount: number;
  balanceBefore: number;
  balanceAfter: number;
  reason: string;
  metadata: CreditTransactionMetadata;
  createdAt: Date;
}

export interface CreateTransactionParams {
  creditAccountId: string;
  spaceId: string;
  type: TransactionType;
  amount: number;
  balanceBefore: number;
  balanceAfter: number;
  reason: string;
  metadata?: CreditTransactionMetadata;
  testClockTime?: Date;
}

/**
 * 积分交易聚合根 - 记录所有积分变动的审计记录
 * 提供完整的积分变动历史和审计追踪
 */
export class CreditTransaction extends AggregateRoot {
  public readonly id: string;
  public readonly creditAccountId: string;
  public readonly spaceId: string;
  public readonly type: TransactionType;
  public readonly creditType: CreditType;
  public readonly amount: number;
  public readonly balanceBefore: number;
  public readonly balanceAfter: number;
  public readonly reason: string;
  public readonly metadata: CreditTransactionMetadata;
  public readonly createdAt: Date;

  private _isNew: boolean = false;

  constructor(params: CreditTransactionConstructorParams) {
    super();
    this.id = params.id;
    this.creditAccountId = params.creditAccountId;
    this.spaceId = params.spaceId;
    this.type = params.type;
    this.creditType = params.creditType;
    this.amount = params.amount;
    this.balanceBefore = params.balanceBefore;
    this.balanceAfter = params.balanceAfter;
    this.reason = params.reason;
    this.metadata = params.metadata;
    this.createdAt = params.createdAt;
  }

  get isNew(): boolean {
    return this._isNew;
  }

  getLanguageModelTransactionCategory(): LanguageModelTransactionCategory | undefined {
    if ('category' in this.metadata) {
      return this.metadata.category;
    }
    return undefined;
  }

  // 静态工厂方法：创建交易记录
  static create(params: CreateTransactionParams): CreditTransaction {
    const now = params.testClockTime || new Date();
    const id = uuidv7();

    const transaction = new CreditTransaction({
      id,
      creditAccountId: params.creditAccountId,
      spaceId: params.spaceId,
      type: params.type,
      creditType: CreditType.MONTHLY, // 当前只支持月度积分
      amount: params.amount,
      balanceBefore: params.balanceBefore,
      balanceAfter: params.balanceAfter,
      reason: params.reason,
      metadata: params.metadata || {},
      createdAt: now,
    });

    transaction.apply(
      new TransactionRecordedEvent(
        id,
        params.creditAccountId,
        params.spaceId,
        params.type,
        params.amount,
        params.reason,
        now,
      ),
    );

    transaction._isNew = true;
    return transaction;
  }

  // 静态工厂方法：创建积分发放交易
  static grant(params: {
    creditAccountId: string;
    spaceId: string;
    amount: number;
    balanceBefore: number;
    reason?: string; // 可选，提供默认值
  }): CreditTransaction {
    // 自动计算 balanceAfter
    const balanceAfter = params.balanceBefore + params.amount;

    // 提供默认 reason
    const reason = params.reason || `Grant ${params.amount} credits`;

    return CreditTransaction.create({
      creditAccountId: params.creditAccountId,
      spaceId: params.spaceId,
      amount: params.amount,
      balanceBefore: params.balanceBefore,
      balanceAfter,
      reason,
      type: TransactionType.GRANT,
    });
  }

  // 静态工厂方法：创建积分消耗交易
  static consume(params: {
    creditAccountId: string;
    spaceId: string;
    amount: number;
    balanceBefore: number;
    balanceAfter: number;
    reason: string;
    metadata?: CreditTransactionMetadata;
    testClockTime?: Date;
  }): CreditTransaction {
    return CreditTransaction.create({
      ...params,
      type: TransactionType.CONSUME,
    });
  }

  // 静态工厂方法：创建积分废弃交易
  static forfeit(params: {
    creditAccountId: string;
    spaceId: string;
    amount: number; // 通常为负数表示废弃
    balanceBefore: number;
    reason?: string; // 可选，提供默认值
  }): CreditTransaction {
    // 自动计算 balanceAfter
    const balanceAfter = params.balanceBefore + params.amount;

    // 提供默认 reason
    const reason = params.reason || `Forfeit ${Math.abs(params.amount)} credits`;

    return CreditTransaction.create({
      creditAccountId: params.creditAccountId,
      spaceId: params.spaceId,
      amount: params.amount,
      balanceBefore: params.balanceBefore,
      balanceAfter,
      reason,
      type: TransactionType.FORFEIT,
    });
  }

  // 查询方法：是否为发放交易
  isGrant(): boolean {
    return this.type === TransactionType.GRANT;
  }

  // 查询方法：是否为消耗交易
  isConsume(): boolean {
    return this.type === TransactionType.CONSUME;
  }

  // 查询方法：是否为废弃交易
  isForfeit(): boolean {
    return this.type === TransactionType.FORFEIT;
  }

  // 实体生命周期方法
  markAsExisting(): void {
    this._isNew = false;
  }
}
