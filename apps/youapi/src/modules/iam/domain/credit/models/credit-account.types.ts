import { LanguageModelUsage } from 'ai';
import Stripe from 'stripe';
import { LLMs } from '@/common/types';
import { ProductTier } from '../../subscription/models/subscription.types';
import type {
  CreditTransactionMetadata,
  LanguageModelTransactionCategory,
} from './credit-transaction.types';

export interface CreditAccountMetadata {
  testClock?: Stripe.TestHelpers.TestClock;
  [key: string]: unknown;
}

export interface CreditAccountConstructorParams {
  id: string;
  spaceId: string;
  monthlyBalance: number;
  refreshCycleAnchor: Date;
  currentPeriodStart: Date;
  currentPeriodEnd: Date;
  productTier: ProductTier;
  createdAt: Date;
  updatedAt: Date;
  metadata?: CreditAccountMetadata;
}

export interface CreateFreeParams {
  spaceId: string;
  testClock?: Stripe.TestHelpers.TestClock;
}

export interface ConsumeCreditsParams {
  amount: number;
  reason: string;
  metadata?: CreditTransactionMetadata;
}

export interface OpenAIProviderMetadata {
  reasoningTokens?: number;
  acceptedPredictionTokens?: number;
  rejectedPredictionTokens?: number;
  cachedPromptTokens?: number;
}

export interface AnthropicProviderMetadata {
  cacheCreationInputTokens?: number;
  cacheReadInputTokens?: number;
}

export interface ProviderMetadata {
  openai?: OpenAIProviderMetadata;
  anthropic?: AnthropicProviderMetadata;
}

export interface BuildMetadataParams {
  model: LLMs;
  usage: LanguageModelUsage;
  providerMetadata: ProviderMetadata;
  totalCredits: number;
}

export interface LanguageModelUsageParams {
  model: LLMs;
  usage: LanguageModelUsage;
  category: LanguageModelTransactionCategory;
  providerMetadata: ProviderMetadata;
}
