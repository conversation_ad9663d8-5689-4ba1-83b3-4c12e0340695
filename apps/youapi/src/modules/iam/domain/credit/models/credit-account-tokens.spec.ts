import { LLMProviders, LLMs } from '@/common/types';
import { CreditAccount } from './credit-account.entity';
import type { LanguageModelUsageParams } from './credit-account.types';
import { LanguageModelTransactionCategory } from './credit-transaction.types';

describe('CreditAccount Token Consumption', () => {
  let account: CreditAccount;

  beforeEach(() => {
    const now = new Date('2024-01-15T10:00:00.000Z');
    const { creditAccount } = CreditAccount.createFree({
      spaceId: 'test-space-id',
    });
    account = creditAccount;
  });

  describe('calculateCreditsByTokens', () => {
    describe('Anthropic Claude models', () => {
      it('should calculate credits for basic Claude usage', () => {
        const params: LanguageModelUsageParams = {
          model: LLMs.CLAUDE_35_SONNET,
          usage: {
            promptTokens: 1000,
            completionTokens: 500,
            totalTokens: 1500,
          },
          providerMetadata: {
            anthropic: {},
          },
          category: LanguageModelTransactionCategory.CHAT,
        };

        const transaction = account.consumeByTokens(params);
        expect(transaction).toBeDefined();

        // Claude 3.5 Sonnet: 3 credits/1k input + 15 credits/1k output = 3 + 7.5 = 10.5 → 11 credits
        expect(transaction!.amount).toBe(11);
      });

      it('should calculate credits for Claude with cache read tokens', () => {
        const params: LanguageModelUsageParams = {
          model: LLMs.CLAUDE_35_SONNET,
          usage: {
            promptTokens: 1000,
            completionTokens: 500,
            totalTokens: 1500,
          },
          providerMetadata: {
            anthropic: {
              cacheReadInputTokens: 800,
            },
          },
          category: LanguageModelTransactionCategory.CHAT,
        };

        const transaction = account.consumeByTokens(params);
        expect(transaction).toBeDefined();

        // 1000 input tokens at 3 credits/1k = 3 credits
        // 500 output tokens at 15 credits/1k = 7.5 credits
        // 800 cache read tokens at 0.3 credits/1k = 0.24 credits
        // Total: 3 + 7.5 + 0.24 = 10.74 → 11 credits
        expect(transaction!.amount).toBe(11);
      });

      it('should calculate credits for Claude with cache creation tokens', () => {
        const params: LanguageModelUsageParams = {
          model: LLMs.CLAUDE_35_SONNET,
          usage: {
            promptTokens: 1000,
            completionTokens: 500,
            totalTokens: 1500,
          },
          providerMetadata: {
            anthropic: {
              cacheCreationInputTokens: 600,
            },
          },
          category: LanguageModelTransactionCategory.CHAT,
        };

        const transaction = account.consumeByTokens(params);
        expect(transaction).toBeDefined();

        // 1000 input tokens at 3 credits/1k = 3 credits
        // 500 output tokens at 15 credits/1k = 7.5 credits
        // 600 cache creation tokens at 3.75 credits/1k = 2.25 credits
        // Total: 3 + 7.5 + 2.25 = 12.75 → 13 credits
        expect(transaction!.amount).toBe(13);
      });

      it('should calculate credits for Claude with both cache read and creation tokens', () => {
        const params: LanguageModelUsageParams = {
          model: LLMs.CLAUDE_35_SONNET,
          usage: {
            promptTokens: 1000,
            completionTokens: 500,
            totalTokens: 1500,
          },
          providerMetadata: {
            anthropic: {
              cacheReadInputTokens: 400,
              cacheCreationInputTokens: 300,
            },
          },
          category: LanguageModelTransactionCategory.CHAT,
        };

        const transaction = account.consumeByTokens(params);
        expect(transaction).toBeDefined();

        // 1000 input tokens at 3 credits/1k = 3 credits
        // 500 output tokens at 15 credits/1k = 7.5 credits
        // 400 cache read tokens at 0.3 credits/1k = 0.12 credits
        // 300 cache creation tokens at 3.75 credits/1k = 1.125 credits
        // Total: 3 + 7.5 + 0.12 + 1.125 = 11.745 → 12 credits
        expect(transaction!.amount).toBe(12);
      });

      it('should calculate credits for Claude Haiku with cache tokens', () => {
        const params: LanguageModelUsageParams = {
          model: LLMs.CLAUDE_35_HAIKU,
          usage: {
            promptTokens: 1000,
            completionTokens: 500,
            totalTokens: 1500,
          },
          providerMetadata: {
            anthropic: {
              cacheReadInputTokens: 200,
              cacheCreationInputTokens: 800,
            },
          },
          category: LanguageModelTransactionCategory.CHAT,
        };

        const transaction = account.consumeByTokens(params);
        expect(transaction).toBeDefined();

        // 1000 input tokens at 0.8 credits/1k = 0.8 credits
        // 500 output tokens at 4 credits/1k = 2 credits
        // 200 cache read tokens at 0.08 credits/1k = 0.016 credits
        // 800 cache creation tokens at 1.0 credits/1k = 0.8 credits
        // Total: 0.8 + 2 + 0.016 + 0.8 = 3.616 → 4 credits
        expect(transaction!.amount).toBe(4);
      });

      it('should handle Claude models with no cache tokens', () => {
        const params: LanguageModelUsageParams = {
          model: LLMs.CLAUDE_37_SONNET,
          usage: {
            promptTokens: 500,
            completionTokens: 250,
            totalTokens: 750,
          },
          providerMetadata: {
            anthropic: {},
          },
          category: LanguageModelTransactionCategory.CHAT,
        };

        const transaction = account.consumeByTokens(params);
        expect(transaction).toBeDefined();

        // 500 input tokens at 3 credits/1k = 1.5 credits
        // 250 output tokens at 15 credits/1k = 3.75 credits
        // Total: 1.5 + 3.75 = 5.25 → 6 credits
        expect(transaction!.amount).toBe(6);
      });
    });

    describe('OpenAI models', () => {
      it('should calculate credits for GPT-4o with cache tokens', () => {
        const params: LanguageModelUsageParams = {
          model: LLMs.GPT_4O,
          usage: {
            promptTokens: 1000,
            completionTokens: 500,
            totalTokens: 1500,
          },
          providerMetadata: {
            openai: {
              cachedPromptTokens: 600,
            },
          },
          category: LanguageModelTransactionCategory.CHAT,
        };

        const transaction = account.consumeByTokens(params);
        expect(transaction).toBeDefined();

        // 1000 input tokens at 2.5 credits/1k = 2.5 credits
        // 500 output tokens at 10 credits/1k = 5 credits
        // 600 cache tokens at 1.25 credits/1k = 0.75 credits
        // Total: 2.5 + 5 + 0.75 = 8.25 → 9 credits
        expect(transaction!.amount).toBe(9);
      });

      it('should calculate credits for o1 models with reasoning tokens', () => {
        const params: LanguageModelUsageParams = {
          model: LLMs.O1_PREVIEW,
          usage: {
            promptTokens: 1000,
            completionTokens: 500,
            totalTokens: 1500,
          },
          providerMetadata: {
            openai: {
              reasoningTokens: 2000,
            },
          },
          category: LanguageModelTransactionCategory.CHAT,
        };

        const transaction = account.consumeByTokens(params);
        expect(transaction).toBeDefined();

        // 1000 input tokens at 15 credits/1k = 15 credits
        // 500 output tokens at 60 credits/1k = 30 credits
        // 2000 reasoning tokens at 60 credits/1k = 120 credits
        // Total: 15 + 30 + 120 = 165 credits
        expect(transaction!.amount).toBe(165);
      });
    });

    describe('Unknown models', () => {
      it('should return undefined for unknown models (free)', () => {
        const params: LanguageModelUsageParams = {
          model: 'unknown-model' as LLMs,
          usage: {
            promptTokens: 1000,
            completionTokens: 500,
            totalTokens: 1500,
          },
          providerMetadata: {},
          category: LanguageModelTransactionCategory.CHAT,
        };

        const transaction = account.consumeByTokens(params);
        expect(transaction).toBeUndefined();
      });
    });

    describe('Edge cases', () => {
      it('should return undefined for zero token usage', () => {
        const params: LanguageModelUsageParams = {
          model: LLMs.CLAUDE_35_SONNET,
          usage: {
            promptTokens: 0,
            completionTokens: 0,
            totalTokens: 0,
          },
          providerMetadata: {
            anthropic: {},
          },
          category: LanguageModelTransactionCategory.CHAT,
        };

        const transaction = account.consumeByTokens(params);
        expect(transaction).toBeUndefined();
      });

      it('should handle very small token counts', () => {
        const params: LanguageModelUsageParams = {
          model: LLMs.CLAUDE_35_SONNET,
          usage: {
            promptTokens: 1,
            completionTokens: 1,
            totalTokens: 2,
          },
          providerMetadata: {
            anthropic: {},
          },
          category: LanguageModelTransactionCategory.CHAT,
        };

        const transaction = account.consumeByTokens(params);
        expect(transaction).toBeDefined();

        // Very small usage should still result in at least 1 credit due to ceiling
        // 1 * 3/1000 + 1 * 15/1000 = 0.003 + 0.015 = 0.018 → 1 credit
        expect(transaction!.amount).toBe(1);
      });

      it('should handle cache tokens without corresponding cache rate', () => {
        // Test a scenario where cache tokens exist but model doesn't have cache pricing configured
        const params: LanguageModelUsageParams = {
          model: LLMs.DEEPSEEK_CHAT, // Free model with no cache pricing
          usage: {
            promptTokens: 1000,
            completionTokens: 500,
            totalTokens: 1500,
          },
          providerMetadata: {
            anthropic: {
              cacheReadInputTokens: 200,
              cacheCreationInputTokens: 300,
            },
          },
          category: LanguageModelTransactionCategory.CHAT,
        };

        const transaction = account.consumeByTokens(params);
        expect(transaction).toBeUndefined(); // Free model should not consume credits
      });
    });
  });

  describe('buildClaudeMetadata', () => {
    it('should include cache creation credits in metadata', () => {
      const params: LanguageModelUsageParams = {
        model: LLMs.CLAUDE_35_SONNET,
        usage: {
          promptTokens: 1000,
          completionTokens: 500,
          totalTokens: 1500,
        },
        providerMetadata: {
          anthropic: {
            cacheReadInputTokens: 200,
            cacheCreationInputTokens: 300,
          },
        },
        category: LanguageModelTransactionCategory.CHAT,
      };

      const transaction = account.consumeByTokens(params);
      expect(transaction).toBeDefined();

      const metadata = transaction!.metadata as any;
      expect(metadata.provider).toBe(LLMProviders.ANTHROPIC);

      // 积分信息
      expect(metadata.cache5mCredits).toBe(1.125); // 300 * 3.75 / 1000 (cacheCreation 按 cache5m 处理)
      expect(metadata.cacheCredits).toBe(0.06); // 200 * 0.3 / 1000 (统一的 cacheRead)

      // Token 使用量
      expect(metadata.tokenUsage).toBeDefined();
      expect(metadata.tokenUsage.cache5mTokens).toBe(300); // cacheCreation 按 cache5m 处理
      expect(metadata.tokenUsage.cacheTokens).toBe(200); // 统一的 cacheRead

      // 价格分解
      expect(metadata.priceBreakdown).toBeDefined();
      expect(metadata.priceBreakdown.cache5mPrice).toBe(3.75);
      expect(metadata.priceBreakdown.cachePrice).toBe(0.3); // 统一的 cache 价格
    });

    it('should handle models without cache creation pricing', () => {
      // Using a model that only has regular cache pricing
      const params: LanguageModelUsageParams = {
        model: LLMs.CLAUDE_35_SONNET,
        usage: {
          promptTokens: 1000,
          completionTokens: 500,
          totalTokens: 1500,
        },
        providerMetadata: {
          anthropic: {
            cacheReadInputTokens: 200,
            // No cacheCreationInputTokens
          },
        },
        category: LanguageModelTransactionCategory.CHAT,
      };

      const transaction = account.consumeByTokens(params);
      expect(transaction).toBeDefined();

      const metadata = transaction!.metadata as any;

      expect(metadata.cache5mCredits).toBe(0);
      expect(metadata.tokenUsage.cache5mTokens).toBe(0);
    });
  });
});
