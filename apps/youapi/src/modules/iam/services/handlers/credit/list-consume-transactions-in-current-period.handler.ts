import { Injectable } from '@nestjs/common';
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, QueryHandler } from '@nestjs/cqrs';
import { ConsumeTransactionsInCurrentPeriodDto } from '@/modules/iam/dto/consume-transactions-in-current-period.dto';
import { CreditAccountRepository } from '@/modules/iam/repositories/credit-account.repository';
import { CreditTransactionRepository } from '@/modules/iam/repositories/credit-transaction.repository';
import { ListConsumeTransactionsInCurrentPeriodQuery } from '../../queries/credit/list-consume-transactions-in-current-period.query';
import { UserDtoService } from '../../user-dto.service';

@Injectable()
@QueryHandler(ListConsumeTransactionsInCurrentPeriodQuery)
export class ListConsumeTransactionsInCurrentPeriodHandler
  implements IQueryHandler<ListConsumeTransactionsInCurrentPeriodQuery>
{
  constructor(
    private readonly creditAccountRepository: CreditAccountRepository,
    private readonly creditTransactionRepository: CreditTransactionRepository,
    private readonly userDtoService: UserDtoService,
  ) {}

  async execute(
    query: ListConsumeTransactionsInCurrentPeriodQuery,
  ): Promise<ConsumeTransactionsInCurrentPeriodDto> {
    const { spaceId } = query;

    // 获取积分账户
    const creditAccount = await this.creditAccountRepository.findBySpaceId(spaceId);
    if (!creditAccount) {
      return {
        transactions: [],
        currentPeriodStart: new Date(),
        currentPeriodEnd: new Date(),
      };
    }

    // 获取当前周期的开始和结束时间
    const currentPeriodStart = creditAccount.currentPeriodStart;
    const currentPeriodEnd = creditAccount.currentPeriodEnd;

    // 查询当前周期的消费记录
    const transactions =
      await this.creditTransactionRepository.findConsumeTransactionsInCurrentPeriodTransactions({
        creditAccountId: creditAccount.id,
        currentPeriodStart,
        currentPeriodEnd,
      });

    // 转换为DTO
    const transactionDtos = transactions.map((transaction) =>
      this.userDtoService.toCreditTransactionDto(transaction),
    );

    return {
      transactions: transactionDtos,
      currentPeriodStart,
      currentPeriodEnd,
    };
  }
}
