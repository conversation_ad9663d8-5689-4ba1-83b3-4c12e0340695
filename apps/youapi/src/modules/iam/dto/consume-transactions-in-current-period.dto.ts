import { ApiProperty } from '@nestjs/swagger';
import { CreditTransactionDto } from './credit-transaction.dto';

export class ConsumeTransactionsInCurrentPeriodDto {
  @ApiProperty({
    description: '当前周期内的积分消费记录',
    type: [CreditTransactionDto],
  })
  transactions: CreditTransactionDto[];

  @ApiProperty({
    description: '当前周期开始时间',
    type: Date,
  })
  currentPeriodStart: Date;

  @ApiProperty({
    description: '当前周期结束时间',
    type: Date,
  })
  currentPeriodEnd: Date;
}
