import { Body, Controller, HttpCode, Post } from '@nestjs/common';
import { ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { BaseController } from '@/shared/base.controller';
import { AdvanceTestClockDto } from '../dto/advance-test-clock.dto';
import { ConsumeCreditsDto } from '../dto/consume-credits.dto';
import { ConsumeCreditsResponseDto } from '../dto/consume-credits-response.dto';
import { ConsumeTransactionsInCurrentPeriodDto } from '../dto/consume-transactions-in-current-period.dto';
import { CreditAccountDto } from '../dto/credit-account.dto';
import { ConsumeCreditsCommand } from '../services/commands/credit/consume-credits.command';
import { AdvanceTestClockCommand } from '../services/commands/test/advance-test-clock.command';
import { GetCreditAccountQuery } from '../services/queries/credit/get-credit-account.query';
import { ListConsumeTransactionsInCurrentPeriodQuery } from '../services/queries/credit/list-consume-transactions-in-current-period.query';

export interface ListTransactionsDto {
  transactionType?: 'GRANT' | 'CONSUME' | 'FORFEIT';
  limit?: number;
  offset?: number;
}

@ApiTags('Credit')
@Controller('api/v1/credit')
export class CreditController extends BaseController {
  @Post('getCreditAccount')
  @HttpCode(200)
  @ApiOperation({
    summary: '查询当前用户的积分账户',
    description: '查询当前用户的积分账户',
  })
  @ApiResponse({
    status: 200,
    description: '查询成功',
    type: CreditAccountDto,
  })
  async getCreditAccount(): Promise<CreditAccountDto> {
    const spaceId = await this.getSpaceId();
    const query = new GetCreditAccountQuery(spaceId);
    return this.queryBus.execute(query);
  }

  // @Post('listCreditTransactions')
  // @HttpCode(200)
  // @ApiOperation({
  //   summary: '查询积分交易记录',
  //   description: '查询当前用户在当前周期内的积分交易记录，支持分页和类型筛选',
  // })
  // async listCreditTransactions(
  //   @Body() dto: ListTransactionsDto = {},
  // ): Promise<ListCreditTransactionsDto> {
  //   const spaceId = await this.getSpaceId();
  //   const query = new ListCreditTransactionsQuery(
  //     spaceId,
  //     dto.transactionType,
  //     dto.limit,
  //     dto.offset,
  //   );
  //   return this.queryBus.execute(query);
  // }

  @Post('listCreditsConsumeTransactionsInCurrentPeriod')
  @HttpCode(200)
  @ApiOperation({
    summary: '查询当前周期内的积分消费记录',
    description: '查询当前用户在当前周期内的所有积分消费记录',
  })
  @ApiResponse({
    status: 200,
    description: '查询成功',
    type: ConsumeTransactionsInCurrentPeriodDto,
  })
  async listCreditsConsumeTransactionsInCurrentPeriod(): Promise<ConsumeTransactionsInCurrentPeriodDto> {
    const spaceId = await this.getSpaceId();
    const query = new ListConsumeTransactionsInCurrentPeriodQuery(spaceId);
    return this.queryBus.execute(query);
  }

  @Post('consumeCredits')
  @HttpCode(200)
  @ApiOperation({
    summary: '消费指定数量的积分',
    description: '从当前用户的积分账户中消费指定数量的积分',
  })
  @ApiResponse({
    status: 200,
    description: '积分消费成功',
    type: ConsumeCreditsResponseDto,
  })
  async consumeCredits(@Body() dto: ConsumeCreditsDto): Promise<ConsumeCreditsResponseDto> {
    const spaceId = await this.getSpaceId();
    const command = new ConsumeCreditsCommand(spaceId, dto.amount);
    return this.commandBus.execute(command);
  }

  @Post('advanceTestClock')
  @HttpCode(200)
  @ApiOperation({
    summary: '将测试时钟推进到指定时间',
    description: '用于测试环境中控制当前用户积分账户的 Stripe 测试时钟时间，推进到指定日期时间',
  })
  @ApiResponse({
    status: 200,
    description: '时钟推进成功',
    type: CreditAccountDto,
  })
  async advanceTestClock(@Body() dto: AdvanceTestClockDto): Promise<CreditAccountDto> {
    const spaceId = await this.getSpaceId();
    const command = new AdvanceTestClockCommand(spaceId, dto.fozenTime);
    return this.commandBus.execute(command);
  }
}
