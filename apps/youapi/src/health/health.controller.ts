import { Controller, Get, Logger, Post } from '@nestjs/common';
import { ApiResponse } from '@nestjs/swagger';
import { PublicRoute } from '@/common/decorators/public.decorator';
import { AppConfigService } from '@/common/services/config.service';
import { BaseController } from '@/shared/base.controller';

@Controller('api/healthz')
export class HealthController extends BaseController {
  protected readonly logger = new Logger(HealthController.name);
  constructor(private readonly configService: AppConfigService) {
    super();
  }

  @PublicRoute()
  @Get()
  health() {
    // 简单健康检查
    return { status: 'ok' };
  }

  @PublicRoute()
  @Get('config')
  @ApiResponse({
    status: 200,
    schema: {
      type: 'object',
      properties: {
        status: {
          type: 'string',
          enum: ['ok'],
        },
        fullConfig: {
          type: 'object',
        },
      },
    },
  })
  config() {
    // 配置测试端点
    return {
      status: 'ok',
      fullConfig: this.configService.getAll(),
    };
  }

  @PublicRoute()
  @Post('config/refresh')
  @ApiResponse({
    status: 200,
    schema: {
      type: 'object',
      properties: {
        status: {
          type: 'string',
          enum: ['ok'],
        },
        message: {
          type: 'string',
        },
        timestamp: {
          type: 'string',
        },
      },
    },
  })
  async refreshConfig() {
    // 手动刷新配置端点 - 从 Doppler API 重新获取配置
    await this.configService.refreshConfig();
    return {
      status: 'ok',
      message: 'Configuration refreshed from Doppler API',
      timestamp: new Date().toISOString(),
    };
  }
}
