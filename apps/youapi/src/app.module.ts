/**
 * 主应用模块
 * 集成了从 youapp 迁移过来的中间件、错误处理、认证等功能
 */

import {
  type MiddlewareConsumer,
  Module,
  type NestModule,
  type OnModuleInit,
} from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { ModuleRef } from '@nestjs/core';

import { CqrsModule } from '@nestjs/cqrs';
import { ScheduleModule } from '@nestjs/schedule';

import * as otel from '@opentelemetry/api';
import { isDeployedRuntime } from '@repo/common';
import { ClsModule } from 'nestjs-cls';
import { LoggerModule } from 'nestjs-pino';
import { AppController } from './app.controller';
import { CommonModule } from './common/common.module';
import { TracingMiddleware } from './common/middleware/tracing.middleware';
import { YouapiClsService } from './common/services/cls.service';
import { getEnvironmentName, getServiceName, getServiceNamespace } from './common/tracing/otel';
import { ApplicationContext } from './common/utils/application-context';
import { DaoModule } from './dao/dao.module';
import { DomainModule } from './domain/domain.module';
import { HealthController } from './health/health.controller';
import { InfraModule } from './infra/infra.module';
import { RedisModule } from './infra/redis/redis.module';
import { AiModule } from './modules/ai/ai.module';
import { ChatModule } from './modules/chat/chat.module';
import { IamModule } from './modules/iam/iam.module';
import { MaterialMngModule } from './modules/material-mng/material-mng.module';
import { SearchModule } from './modules/search/search.module';
import { TestModule } from './modules/test/test.module';
import { FixJsonInFormUrlencodedMiddleware } from './shared/fix-json-in-form-urlencoded.middleware';

@Module({
  imports: [
    // 配置模块必须放在最前面
    ConfigModule.forRoot({
      isGlobal: true,
      cache: true,
      envFilePath: [`.env.${process.env.YOUMIND_ENV}.local`, `.env.${process.env.YOUMIND_ENV}`],
    }),

    // CLS 模块用于管理请求级别的数据
    ClsModule.forRoot({
      global: true,
      middleware: { mount: true },
    }),

    // Pino Logger 模块用于结构化日志和 New Relic 集成
    LoggerModule.forRootAsync({
      useFactory: (clsService: YouapiClsService) => ({
        pinoHttp: {
          level: process.env.NODE_ENV === 'production' ? 'info' : 'debug',
          autoLogging: true,
          genReqId: () => clsService.getTraceId() || '',
          customErrorMessage(req, res) {
            return `[${req.method}] ${req.url} ${res.statusCode}`;
          },
          customSuccessMessage(req, res) {
            return `[${req.method}] ${req.url} ${res.statusCode}`;
          },
          customProps: () => {
            const span = otel.trace.getActiveSpan();
            const spanContext = span?.spanContext();

            const extraAttrs = isDeployedRuntime()
              ? {
                  'service.name': getServiceName(),
                  'service.namespace': getServiceNamespace(),
                  'deployment.environment.name': getEnvironmentName(),
                }
              : {};

            return {
              user_id: clsService.getUserId(),
              space_id: clsService.getSpaceId(),
              'trace.id': spanContext?.traceId,
              'span.id': spanContext?.spanId,
              'trace.flags': spanContext?.traceFlags,
              ...extraAttrs,
            };
          },
          serializers: {
            req: (req) => ({
              method: req.method,
              url: req.url,
            }),
            res: (res) => ({
              statusCode: res.statusCode,
            }),
          },
          formatters: {
            level: (label) => ({ level: label }),
          },
          // 开发环境使用 pretty 格式，生产环境使用 JSON
          transport:
            process.env.NODE_ENV === 'development'
              ? {
                  target: 'pino-pretty',
                  options: {
                    colorize: true,
                    translateTime: 'SYS:HH:MM:ss.l',
                    ignore: 'pid,hostname',
                    singleLine: true,
                  },
                }
              : undefined,
        },
      }),
      inject: [YouapiClsService],
    }),

    // DevtoolsModule.register({
    //   http: process.env.NODE_ENV === 'development',
    // }),

    // CQRS 模块用于处理命令和事件
    CqrsModule.forRoot(),

    // 定时任务模块
    ScheduleModule.forRoot(),

    // 通用模块提供中间件、日志等服务
    CommonModule,

    // 数据访问模块
    DaoModule,

    // 基础设施模块提供三方服务
    InfraModule,

    // 领域模块提供业务服务
    DomainModule,

    RedisModule,

    MaterialMngModule,
    IamModule,
    ChatModule,
    SearchModule,
    AiModule,
    TestModule,
  ],
  controllers: [AppController, HealthController],
})
export class AppModule implements NestModule, OnModuleInit {
  constructor(private moduleRef: ModuleRef) {}

  onModuleInit() {
    ApplicationContext.register(this.moduleRef);
  }

  configure(consumer: MiddlewareConsumer) {
    // 配置全局中间件
    consumer.apply(TracingMiddleware, FixJsonInFormUrlencodedMiddleware).forRoutes('*'); // 应用到所有路由
  }
}
